import asyncio
import itertools
import json
from datetime import datetime, timezone
from functools import partial
from typing import Any, Coroutine

from langchain_core.messages import AIMessage, BaseMessage, FunctionMessage, HumanMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.graph.graph import CompiledGraph

from baml_client import b
from baml_client.types import ResponseAllPreferences
from llm_utils.llm_utils import get_message_buffer_as_strings
from preferences_agent.models import AgentState
from preferences_agent.prompts import (
    cal_analysis_opening_string,
    no_cal_analysis_opening_string,
    preferences_pre_analysis_message,
    preferences_traveler_prod_message,
)
from server.schemas.authenticate.user import User
from server.services.user.user_preferences import get_user_preferences, save_user_preferences
from server.utils.logger import logger
from server.utils.settings import settings
from server.utils.websocket_no_op import partial_no_op
from virtual_travel_agent.helpers import console_masks
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import Timings

config: RunnableConfig = {"configurable": {"recursion_limit": 10}}

# This will load and use local test data vs use the data that comes
# directly from the user's calendar
# should_load_test_data = False
other_log_mask = console_masks["other"]


class TravelAgentPreferences:
    def __init__(
        self,
        user: User,
        thread_id: int,
        websocket_send_message: partial[Coroutine[Any, Any, None]] | None,
    ):
        self.websocket_send_message = websocket_send_message if websocket_send_message is not None else partial_no_op

        self.user: User = user
        self.thread_id = thread_id
        # self.should_load_test_data = should_load_test_data
        self.do_historical_calendar_analysis = False  # JTB:  Future-tense
        self.filtered_travel_calendar_events = None
        # Add memory
        self.history = PostgresChatMessageHistory(thread_id=thread_id)

        self.messages: list[BaseMessage] = []

        # Define a new graph
        self.workflow: StateGraph = StateGraph(AgentState)

        self.workflow.add_node("preferences_convo_agent", self.preferences_convo_agent_model_function)
        self.workflow.add_edge("preferences_convo_agent", END)

        self.workflow.add_node("preferences_cal_analysis_agent", self.preferences_cal_analysis_agent_model_function)
        self.workflow.add_edge("preferences_cal_analysis_agent", "preferences_convo_agent")

        self.workflow.set_conditional_entry_point(self.supervisor_handle_next_message)

        self.graph: CompiledGraph = self.workflow.compile()

    # Define the function that determines whether to continue or not
    # The suoervisor calls this after it invokes it's LLM
    def supervisor_handle_next_message(self, state):
        if self.do_historical_calendar_analysis:
            return "preferences_cal_analysis_agent"
        return "preferences_convo_agent"

    async def preferences_convo_agent_model_function(self, state):
        messages = []
        for idx, message in enumerate(reversed(state["messages"])):
            if isinstance(message, HumanMessage):
                messages.append(message)  # only respond to user's last message
                break

        base_user_preference = await get_user_preferences(self.user.id)
        message_buffer_strs = get_message_buffer_as_strings(messages)
        did_historical_calendar_analysis = state.get("did_historical_calendar_analysis")
        # The preferences conversational prompt depends on whether a calendar
        # analysis was done or not.
        if did_historical_calendar_analysis:
            t = Timings("BAML: ConversePreferencesPostAnalysis")
            response = await b.ConversePreferencesPostAnalysis(
                existing_preference=base_user_preference.model_dump_json(),
                cal_events=None,
                messages=message_buffer_strs,
                user_name=self.user.name,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("purple")
            logger.log_baml()
        else:
            pref_dict = base_user_preference.model_dump()
            missing_preferences = [k for k in pref_dict.keys() if not pref_dict[k]]
            t = Timings("BAML: ConversePreferencesWithoutAnalysis")
            response = await b.ConversePreferencesWithoutAnalysis(
                existing_preference=base_user_preference.model_dump_json(exclude_none=True),
                cal_events=None,
                missing_preferences=missing_preferences,
                messages=message_buffer_strs,
                user_name=self.user.name,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("purple")
            logger.log_baml()

        # Hack in cleaner line feeds that the webapp will
        response.agent_response = response.agent_response.replace("\n\n", "&NewLine; &NewLine;")
        new_message = AIMessage(content="")
        new_message.additional_kwargs = {"function_call": {"arguments": response.model_dump_json()}}
        new_message.content = response.agent_response
        user_preferences = response.response_all_preferences
        asyncio.create_task(save_user_preferences(self.user.id, user_preferences))
        await self.add_timestamp_message()
        return {"messages": [new_message]}

    async def preferences_cal_analysis_agent_model_function(self, state):
        messages = state["messages"]
        message_buffer_strs = get_message_buffer_as_strings(messages)
        calendar_events_str = json.dumps(self.filtered_travel_calendar_events)
        # logger.info(calendar_events_str, mask="\033[94m {}\033[00m")

        base_user_preference = await get_user_preferences(self.user.id)
        t = Timings("BAML: ConversePreferencesWithHistoricalAnalysis")
        response = await b.ConversePreferencesWithHistoricalAnalysis(
            existing_preference=base_user_preference.model_dump_json(),
            cal_events=calendar_events_str,
            messages=message_buffer_strs,
            user_name=self.user.name,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        t.print_timing("purple")
        logger.log_baml()

        new_message = AIMessage(content="")
        json_representation = response.model_dump_json()
        new_message.additional_kwargs = {"function_call": {"arguments": json_representation}}

        # Last response from the model
        # logger.info(str(new_message), mask="\033[95m {}\033[00m")

        new_message.content = response.agent_response

        # Set some flags
        self.do_historical_calendar_analysis = False  # We won't do it again

        # We return a list, because this will get added to the existing list
        return {"messages": [new_message], "did_historical_calendar_analysis": True}

    async def run(self, message=None, message_type="text", extra_payload=None):
        to_send: list[dict[str, str | bool | None]] = []

        if message is None:
            to_send = await self.get_history_messages()

            if len(self.messages) == 0:
                if self.do_historical_calendar_analysis:
                    targeted_opening_string = cal_analysis_opening_string
                else:
                    targeted_opening_string = no_cal_analysis_opening_string

                agent_response = AIMessage(content=targeted_opening_string.format(user_name=self.user.name).strip())

                # TODO: (chengxuan.wang) will revisit this since we probalby don't need
                # to do calendar analysis in preference tab anymore.
                if self.do_historical_calendar_analysis:
                    agent_response = AIMessage(content=preferences_pre_analysis_message)
                    self.history.add_pending_message(agent_response)
                    self.messages.append(agent_response)
                    await self.websocket_send_message(
                        message={
                            **self.map_websocket_message(agent_response)[0],
                            "expectResponse": False,
                        }
                    )

                    agent_response = HumanMessage(content=preferences_traveler_prod_message)
                    agent_response.additional_kwargs["message_type"] = "silent_prompt"
                    self.history.add_pending_message(agent_response)
                    self.messages.append(agent_response)

            elif isinstance(self.messages[-1], AIMessage):
                # In the previous session we were waiting for user input,
                # return the entire history from previous session
                to_send[-1]["expectResponse"] = True
                return to_send
            else:
                # In the previous session we were waiting for LLM, wait for LLM
                # new message then return it with the entire history from
                # previous session
                agent_response = self.messages.pop()

                history_message = {
                    "type": "history",
                    "messages": [{**message, "is_history": True} for message in to_send],
                }

                await self.websocket_send_message(message=history_message)
                to_send = []
        else:
            if message_type == "update":
                agent_response = HumanMessage(
                    content=", ".join(map(str, message)),
                    additional_kwargs={"is_card_update": True},
                )
            elif message_type == "silent_prompt":
                agent_response = HumanMessage(content=message)
                agent_response.additional_kwargs["message_type"] = message_type
            else:
                agent_response = HumanMessage(content=message)
            self.history.add_pending_message(agent_response)
            self.messages.append(agent_response)

        response = await self.graph.ainvoke({"messages": list(self.messages), "input": agent_response}, config=config)

        lastest_model_message = response["messages"][-1]
        self.history.add_pending_message(lastest_model_message)
        self.messages.append(lastest_model_message)

        to_send += self.map_websocket_message(lastest_model_message)

        return to_send

    def map_websocket_message(self, message: BaseMessage) -> list[dict[str, str | bool | None]]:
        is_bot_message: bool = isinstance(message, AIMessage) or isinstance(message, FunctionMessage)

        messages: list[dict[str, Any]] = []
        try:
            message_dict: dict[str, str] = json.loads(str(message.content)) if "{" in str(message.content) else {}
            messages.append(
                {
                    "type": message.additional_kwargs.get("message_type", "prompt"),
                    "text": message_dict["agent_response"]
                    if "agent_response" in message_dict.keys()
                    else str(message.content),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""), None
                    ),
                }
            )

        except ValueError:
            messages.append(
                {
                    "type": message.additional_kwargs.get("message_type", "prompt"),
                    "text": str(message.content),
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""), None
                    ),
                }
            )

        except Exception as e:
            logger.error(f"Error parsing message: {e}")

            messages.append(
                {
                    "type": message.additional_kwargs.get("message_type", "prompt"),
                    "text": message.content,
                    "expectResponse": True,
                    "isBotMessage": is_bot_message,
                    "textColor": settings.AGENT_MESSAGE_COLOR_MAP.get(
                        message.additional_kwargs.get("agent_classification", ""), None
                    ),
                }
            )

        return messages

    async def add_timestamp_message(self, new_timestamp: str | None = None, send_ws_message: bool = True):
        if new_timestamp is None:
            new_timestamp = datetime.now(timezone.utc).isoformat()

        # if datetime.fromisoformat(new_timestamp) - self.history.max_created_date > settings.MIN_MESSAGE_TIMESTAMP_DELTA:
        timestamp_message = AIMessage(content="", additional_kwargs={"timestamp": new_timestamp})
        self.history.add_pending_message(timestamp_message)

        if send_ws_message:
            await self.websocket_send_message(
                message={"type": "prompt", "timestamp": timestamp_message.additional_kwargs.get("timestamp")}
            )

    async def get_history_messages(self):
        self.messages = await self.history.persisted_messages

        history: list[list[dict[str, str | bool | None]]] = []
        for message in self.messages:
            if message.additional_kwargs.get("is_card_update", False):
                continue
            elif message.additional_kwargs.get("timestamp", False):
                history.append([{"type": "prompt", "timestamp": message.additional_kwargs.get("timestamp")}])
            else:
                history.append(self.map_websocket_message(message))

        # Filter out timestamp messages
        self.messages = [m for m in self.messages if not m.additional_kwargs.get("timestamp", False)]

        # Flatten the nested list of history messages into a single list for easier processing
        to_send = list(itertools.chain.from_iterable(history))

        # This is history, do not wait for user input
        for msg in to_send:
            msg["expectResponse"] = False

        return to_send
