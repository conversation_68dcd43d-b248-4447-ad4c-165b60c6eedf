class FrequentFlierNumber {
  airline_code string @description("The IATA code for the airline the frequent flyer number is associated with.")
  number string @description("The frequent flyer number.")
}

class FlightBookingResponse {
  agent_response string @description("a message to the traveler confirming the flight booking and the airline_confirmation_number if available")
  error_response string? @description(#"If there was an error_response provided then respond back to the traveler in a friendly tone that the flights couldn't be booked and to try again later. Also include an error_response text extracted summarizing the error. e.g. 'The selected itinerary is no longer available. Please try start search from beginning again.'
    please ignore warning if the status is success.
  "#)
}

class ChangeFlight {
  start_ariport_code string @description("The IATA code for the airport the traveler want to change for the flight as start airport.")
  end_airport_code string @description("The IATA code for the airport the traveler want to change for the flight as end airport.")
  date string @description("The date the traveler want to change for the flgiht to be on. It is ISO 8601 format, yyyy-mm-ddThh:mm:ss.")
  is_newly_add bool @description("True if this flight is newly added, otherwise False. It is usually happend to add return flight for one way flgiht.")
}

template_string ProcessFlightSearchRules(preferred_flight_cabin: string?) #"
    ---
    **Flight Selection Order:**
    **Step 1: Honor the user's explicitly stated request in this order:**
      1. **Fare class**  → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
      2. **Airline** → If not specified, check user preference, if user preference of airline is also not avaiable, check the default airline codes for the departing airport. 
      3. **Stops** → If the user explicitly requests a stopover (e.g., "I want a stop in Chicago"), honor that **even if their saved preference is nonstop**.
      4. **Departure time window** (if specified) → Strictly enforce this preference.
      5. **Arrival time window** (if specified).

    **Step 2: If no explicit request is given, honor user preferences:**
      1. **Fare class** → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
      2. **Preferred airlines** (saved preferences).
      3. **Stops** → If none, default to **Nonstop**.

    **Step 3: If no user preferences exist, use built-in business traveler assumptions:**
      1. **Fare class** → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
      2. **Airline:** Prioritize 'home-airlines' based on departure airport.
      3. **Stops:** Default to Nonstop.

    **Step 4: Ensure a total of six unique flight options are always provided**
    - **First, select all non-stop flights (0-stop), ensuring only one option per flight number.**
      - If multiple fare classes exist for the same flight, pick **only one** using this priority:
        1. **Exact match to user request** (if specified).
        2. **Preferred fare class (if provided)**.
        3. **Lowest cost if no clear preference**.
    - **Ensure every selected flight has a unique flight number at all cost.**
      - If two options with the same flight number exist, **keep only the highest-ranked option and replace the duplicate with a different flight number.**
      - You can dedup by using the 'dedup_key' field in the data.
    - **If no non-stop flights exist, explicitly inform the traveler but continue selecting the best alternatives.**
    - **Then, if fewer than six options are available, add layover flights that match at least one preference:**
      1. **Same airline** (if a preferred airline exists).
      2. **Same fare class** (e.g., Business if requested).
      3. **Shortest layovers** (minimizing total travel time).

    **Step 5: Rank the selected flights by below order:**
      1. Place flights that best match the user’s specific preferences at the top. 
        This includes: cabin, stops, airline/alliance, departure/arrival time window, cancellation policy
        Note: use the departure_time or arrival_time fields in the data to determine if the flight matches the user's time preferences.
      2. Among flights with the same level of preference match, rank by **Price** (lowest to highest)
      3. If there is still a tie, sort by **duration** (shortest to longest)
      NOTE:Don’t consider any other criteria (eg. convienience unless user explicitly asks) if it is listed above.
"#


class SelectedFlight {
  reason string @description(#"
    Summarize the reason why this flight is selected than others in the csv data.
    Highlight its key advantages COMPARED to other flights in categories in BULLET POINTS.
    each bullet point should be 10 words or less and keep the top 3 bullet points.
    The reason should be compelling and highlight its key advantages, also please take business traveler common knowledge into consideration when generating reason. 
    If the flight airline code is in traveler's preferred airline code list, instead of saying it is the preferred airline, say you can accumulate mileage on your program. If the flight airline code is not in traveler's preferred airline code list, please don't include the mileage accrual in the reason.
    If no exact match was found for user preferences, explain why this alternative was chosen despite not being optimal.
  "#)
  // why string? @description(#"
  //   why you include "You can accumulate mileage on your program" in reason

  // "#)  
  index_id int @description("The index_id of the preferred flight. Return the field 'index_id' from the input csv data.")
  is_red_eye bool @description("Whether this flight is a red-eye (departs in the evening and arrives in the morning).")
}

class SingleFareResponse {
  selected_flight SelectedFlight | null @description("The best fare option from this flight group.")
  // flight_keywords string[] | null @description("Time-based keywords for the selected flight.")
}

class FlightSearchResponse_v2 {
  fc_1 SelectedFlight | null @alias(first_choice) @description("The first choice of the preferred flight.")
  fc_2 SelectedFlight | null @alias(second_choice) @description("The second choice of the preferred flight.")
  fc_3 SelectedFlight | null @alias(third_choice) @description("The third choice of the preferred flight.")
  fc_4 SelectedFlight | null @alias(fourth_choice) @description("The fourth choice of the preferred flight.")
  fc_5 SelectedFlight | null @alias(fifth_choice) @description("The fifth choice of the preferred flight.")
  fc_6 SelectedFlight | null @alias(sixth_choice) @description("The sixth choice of the preferred flight.")
  flight_keywords string[][] | null @description(#"
    For each selected flight, generate one time-based, lowercase keyword to characterize the flight. Choose from the below list:
      - 'early-morning' (4:00 am – 7:59 am)
      - 'morning' (8:00 am – 11:59 am)
      - 'afternoon' (12:00 pm – 5:59 pm)
      - 'evening' (6:00 pm – 7:59 pm)
      - 'night' (8:00 pm – 11:59 pm)
      - 'late-night' (12:00 am – 3:59 am)
    Note:
    - Do not use 'red-eye' as a keyword.
    - Each flight should have one keyword or empty array if no keyword is applicable.
    - Only add it if it stands out for this specific flight. If all selected flights share the same characteristic (e.g., all are morning), do not add this keyword.
  "#)
  error_response string? @description("An error message back to the traveler in a friendly tone when no flight choices were returned and that they could try changing their search criteria or try again later.")
  @@dynamic
  //For debug
  // explain string[] @description("Why AS airline rank before HA airline?")
}

template_string PremiumFlightSelectRules(preferred_flight_cabin: string?) #"
    **Step 2: Selection based on priority (in strict order):**
        1. **Fare Class**
          - Must be {{ preferred_flight_cabin |  default("BUSINESS") }} class if available.
          - If unavailable, offer next best option.
        2. **Flight Stops**
          - Prioritize non-stop flights
          - Only consider flights with stops if fewer than 6 non-stop options exist or user requests
        3. **Airline Selection:**
          - Only consider specific airlines if explicitly requested
          - Ignore airline loyalty/alliances unless specifically mentioned
        4. **Time Window**
          - If user specifies a preferred departure or arrival time window, select flights that match this window as the primary options.
          - Additionally, if there are fewer than 6 flights matching the time window, include a few alternatives that fall outside this time window in case user wants flexibility.
	        - If no time preference is given, select flights across different parts of the day (e.g., morning, afternoon, evening) to give user a varied set of options.

      **If No Matches Found:**
        1. First try: closest alternatives that are exchangeable
        2. Second try: best available exchangeable options
        3. Always include explanation for why perfect matches weren't available
"#

template_string BasicFlightSelectRules(preferred_flight_cabin: string?) #"
    **Step 2: Flight Selection Process**
      **Selection Criteria:**
        **A. Process explicit user requests (in strict order)**
          1. **Fare Class**: {{ preferred_flight_cabin | default("ECONOMY") }}
            - If the requested fare class is unavailable, offer the next best alternative fare class. eg. if prenium economy is not available, offer economy. *Never offer BASIC_ECONOMY as an alternative.*
          2. **Airline**: 
          - Check in below order:
            - Explicitly specified airline
            - User's airline preference
            - Default airline codes for departure airport
          3. **Stops**: Honor specific stopover requests (e.g., "stop in Chicago")
            - Override saved nonstop preferences if stopover explicitly requested
          4. **Time Windows**:
            - If user specifies a preferred departure or arrival time window, select flights that match this window as the primary options.
            - Additionally, if there are fewer than 6 flights matching the time window, include a few alternatives that fall outside this time window in case user wants flexibility.
            - If no time window specified, and other creteria are tied for selected flights, select the flight that departs in different time of the day.

        **B. Handle No Explicit Requests (use saved preferences)**
          1. **Fare class** → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
          2. **Preferred airlines** (saved preferences).
          3. **Stops** → If none, default to **Nonstop**.

        **C. Default business rules (if no preferences exist)**
          1. **Fare class** → Pick {{ preferred_flight_cabin | default("ECONOMY") }} if available.
          2. **Airline:** Prioritize 'home-airlines' based on departure airport.
          3. **Stops:** Default to Nonstop.
      
      **Finial Selection Rules:**
      - Maximum selections: Up to 6 unique flights from the ranked list
      - Show all matching flights (even if only 1-2 flights qualify)
      - Only expand selection criteria if ZERO flights found
      - If 1+ flights found, maintain original strict criteria
          - **Ensure every selected flight has a unique flight number at all cost.**
        - If two options with the same flight number exist, **keep only the highest-ranked option and replace the duplicate with a different flight number.**
      - **If no non-stop flights exist, explicitly inform the traveler but continue selecting the best alternatives.**
      - **Then, if fewer than six options are available, add layover flights that match at least one preference:**
        1. **Same airline** (if a preferred airline exists).
        2. **Same fare class** (e.g., Business if requested).
        3. **Shortest layovers** (minimizing total travel time).
"#


class RankedFlight {
  dedup_key string @description("Unique key of the selected flight")
  ranking_reason string @description("Why this flight was ranked highly based on route/schedule criteria. keep it short in 15 words or less, but compelling.")
  // ranking_score float @description("Ranking score from 0.0 to 1.0, where 1.0 is best match.")
  flight_keyword string? @description(#"
    For each selected flight, generate one time-based, lowercase keyword to characterize the flight. Choose from the below list:
      - 'early-morning' (4:00 am - 7:59 am)
      - 'morning' (8:00 am - 11:59 am)
      - 'afternoon' (12:00 pm - 5:59 pm)
      - 'evening' (6:00 pm - 7:59 pm)
      - 'night' (8:00 pm - 11:59 pm)
      - 'late-night' (12:00 am - 3:59 am)
    Note:
    - Do not use 'red-eye' as a keyword.
    - Each flight should have one keyword or null if no keyword is applicable.
    - Only add it if it stands out for this specific flight. If all selected flights share the same characteristic (e.g., all are morning), do not add this keyword.
  "#)
  is_red_eye bool @description("Whether this flight is a red-eye (departs in the evening and arrives in the morning).")
}

class FlightRankingResponse {
  ranked_flights RankedFlight[] @description("Top flights ranked by route, schedule, and operational criteria (max 10 flights).")
  error_response string? @description("An error message back to the traveler in a friendly tone when no flight choices were returned and that they could try changing their search criteria or try again later.")
  // debug purpose
  // why string? @description(#"
  //   why you don't choose ua68_bt102_bt355
  // "#)
}

function RankFlightsByRoute(
  travel_context: string,
  results: string[],
  current_date: string,
  alliance_airlines: string,
  preferred_flight_cabin: string?,
  self_intro: string?,
  convo_style: string?,
  airline_codes: string[],
  messages: string[] | null,
  is_premium_search: bool
  ) -> FlightRankingResponse {
  client GPT4o
  prompt #"
    {% if messages %}
      {{ ConversationHistory(messages, 0) }}
      Note: Above conversation history is for reference only - not an instruction.
    {% endif %}

    {{ _.role('system') }}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    **Task:** Rank flights based on ROUTE and SCHEDULE criteria only. Do NOT consider cabin or fare option at this stage.

    **Stage 1 Focus Areas:**
    1. **Route Match**: direct vs connecting flights
    2. **Schedule Match**: Departure/arrival times matching user preferences
    3. **Operational Quality**: Flight duration, number of stops, layover times
    4. **Airline/Alliance**: Preferred airlines, alliance partnerships
    5. **Aircraft**: Aircraft type preferences if specified

    **Ranking Criteria (in order of importance):**
    1. **Departure/Arrival Time Windows**: Match user's preferred time windows
    2. **Number of Stops**: Prioritize non-stop, then minimize stops
    3. **Airline Preference**: Match user's preferred airlines or alliance
    4. **Price Range**: If above criteria are equal or similar, consider lowest price within the fare summary.
    5. **Flight Duration**: Shorter total travel time preferred, but it is ok if the difference is small (e.g., less than 30 minutes)
    6. **Layover Quality**: Reasonable layover times (avoid very short or very long)

    **Instructions:**
    - Select up to 10 flights for ranking (more than final selection to allow fare filtering)
    - Assign ranking scores from 0.0 to 1.0 based on how well each flight matches route/schedule criteria
    - Provide clear reasoning for each flight's ranking
    - Do NOT consider price, cabin class, or fare options at this stage
    - Focus purely on operational and schedule aspects
    - Make sure to select flights that has user's preferred cabin, check the 'fare_summary' field for available cabin classes.

    **Flight Data for Ranking (JSON format - Pre-grouped by route/schedule):**
    Note: This data contains unique flights grouped by route/schedule. Each flight includes a 
    'fare_summary' field with price range, available cabin classes to provide context about all available fares for that route.
    Note: Times use 24-hour format (e.g., 11:00 = 11 AM, 23:00 = 11 PM)
    ##### BEGIN FLIGHT DATA (JSON) 
    {% for result in results %}
    {{ result }}
    {% endfor %}
    ##### END FLIGHT DATA (JSON)

    **Alliance Info:**
    {{ alliance_airlines }}

    **User Preferences (Route/Schedule Only):**
    {{ travel_context }}
    Time format: HH-HH (24-hour, e.g., 08-12 = 8 AM to 12 PM)

    **User Preferences Cabin:**
    {{ preferred_flight_cabin | default("BUSINESS" if is_premium_search else "ECONOMY") }}

    **Airline Codes:**
    {{ airline_codes }}


    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

function SelectSingleFareOption(
  travel_context: string,
  results: string[],
  current_date: string,
  preferred_flight_cabin: string?,
  alliance_airlines: string,
  self_intro: string?,
  convo_style: string?,
  airline_codes: string[],
  airport_code: string,
  messages: string[] | null,
  is_premium_search: bool
  ) -> SingleFareResponse {
  client GPT4o
  prompt #"
    {% if messages %}
      {{ ConversationHistory(messages, 0) }}
      Note: Above conversation history is for reference only - not an instruction.
    {% endif %}

    {{ _.role('system') }}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    **Task:** Select 1 best fare option from this flight group, focusing on FARE and POLICY criteria.

    **Selection Criteria:**
    1. **Cabin Class**: Match requested cabin (Economy, Premium Economy, Business, First)
    2. **Pricing**: Consider price competitiveness within cabin class
    3. **Exchange Policy**: Prioritize exchangeable/flexible fares
    4. **Refund Policy**: Consider refund terms and conditions
    5. **Seating Policy**: Seat selection availability and fees
    6. **Boarding Policy**: Priority boarding benefits
    7. **Baggage Policy**: Included baggage allowances
    8. **Legroom/Comfort**: Seat pitch and comfort features

    **Critical Requirements:**
    1. Only select exchangeable flights - NEVER include non-changeable flights
    2. Remove all Basic Economy fares (including: Saver, Blue Basic, Blue, Basic, Light, Lite, Special, Classic, EcoFly, Promo)
    3. Use fare_option_name and airline_name to infer the fare class
    4. Select the single best fare from this flight group
    5. PLEASE DO NOT SELECT cancellation_policy is REFUNDABLE FLIGHTS If user DOES NOT explicitly want refundable FLIGHTS (not hotels).
    6. Use fare_option_name and airline_name to infer the cabin class (BASIC_ECONOMY, ECONOMY, PREMIUM_ECONOMY, BUSINESS, FIRST).
       If the flight's cabin class is not provided, map the flight option name to the correct cabin class using the "Airline Knowledge" below.
       If the information in the "Airline Knowledge" section is not sufficient to map the flight to a cabin class, use standard airline industry knowledge—including fare structures, airline branding —to infer the most likely cabin class.

    {{ AirlineKnowledge() }}

    **Selection Process:**
    1. Filter by cabin class preference: {{ preferred_flight_cabin | default("BUSINESS" if is_premium_search else "ECONOMY") }}
    2. Remove non-exchangeable and Basic Economy options
    3. Select 1 best fare balancing ranking score with fare quality

    **Flight Data (JSON format):**
    ##### BEGIN FLIGHT DATA (JSON) 
    {% for result in results %}
    {{ result }}
    {% endfor %}
    ##### END FLIGHT DATA (JSON)

    **User Preferences:**
    {{ travel_context }}

    **Cabin Class:**
    {{ preferred_flight_cabin | default("BUSINESS" if is_premium_search else "ECONOMY") }}

    **Alliance Info:**
    {{ alliance_airlines }}

    **Airline Codes:**
    {{ airline_codes }}

    **Airport:**
    {{ airport_code }}

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

template_string FlightRankingRules() #"
    Rank the selected flights by below order:**
      1. Place flights that best match the user’s specific preferences at the top. 
        This includes: cabin, stops, airline/alliance, departure/arrival time window, cancellation policy
        Note: use the departure_time or arrival_time fields in the data to determine if the flight matches the user's time preferences.
        Note: HA and AS are considered the same airline, rank them as the same if other criteria are the same, even user explicitly asks for HA or AS.
      2. Among flights with the same level of preference match, rank by **Price** (lowest to highest), the flights for selection are already sorted by price (lowest to highest), the one with the lowest price should be the first one.
      3. If there is still a tie, sort by **duration** (shortest to longest)
      4. If departure/arrival time is specified but no flights excatly match the time window, put the flights that are closest to the time window before other flights.
      NOTE: Don't consider any other criteria (eg. convienience unless user explicitly asks) if it is listed above.
"#

template_string AirlineKnowledge() #"
    **Airline Knowledge:**
    - Some fare class names vary by airline:
      - Delta Airline Fare Class Mapping:
          - Delta Main Basic: Basic Economy 
          - Delta Main/ Delta Main Classic/ Delta Main Extra: Economy
          - Delta Comfort: Premium Economy
          - Delta Premium Select: Premium Economy
          - Delta First: First Class (domestic or select international)
          - Delta One: Business Class (international and select transcontinental)
      - JetBlue Fare Class Mapping:
        - Blue Basic: Basic Economy
        - Blue / Blue Plus/ Blue Extra: Economy
        - Mint: Business Class
    - Alaska Airlines (AS) and Hawaiian Airlines (HA) are now operated as a single carrier for booking and alliance purposes. If a user has a preference for either, consider both airlines.
"#

template_string BusinessTravelerKnowledge() #"
    **Business Travel Common Knowledge**

    - **Punctuality is critical:** Business travelers generally value on-time performance and minimal risk of delays or missed connections, as their schedules are often tight.
    - **Schedule flexibility matters:** Options that allow changes (i.e., not basic economy) are strongly preferred, given the possibility of last-minute meeting changes or emergencies.
    - **Time Windows:** Business travelers often have specific time windows for departure and arrival -- usually as depart after 9Am, arrive before 7Pm.
    - **Loyalty and status:** Many business travelers belong to frequent flyer programs; flights on preferred airlines or alliances may earn more loyalty points or status benefits.
    - **Amenities and comfort:** Access to priority boarding, extra legroom, Wi-Fi, power outlets, and in-cabin workspace can be important for productivity and comfort.
    - **Baggage allowance:** Business travelers often travel with at least one carry-on and sometimes a checked bag (e.g., for presentation materials or longer stays). Fares with included baggage are preferred.
    - **Seating:** Advance seat selection, aisle seats, and proximity to the front of the cabin are often desirable for convenience and quick exit.
    - **Airport experience:** Shorter layovers, access to lounges, and flights from more convenient airports can reduce travel stress and maximize productivity.
    - **Total travel time:** Shorter itineraries and less time in transit are preferred, even if the fare is not the absolute cheapest.
    - **Travel policy compliance:** Some companies require booking within a certain fare class or with specific airlines—compliance may be a consideration.
    - **Cost consciousness:** While not as price-sensitive as leisure travelers, business travelers still value reasonable fares and avoiding unnecessary upgrades.
"#

function ProcessFlightSearchResults_v2(
  travel_context: string,
  results: string,
  current_date: string,
  alliance_airlines: string,
  self_intro: string?,
  convo_style: string?,
  preferred_flight_cabin: string?,
  user_preferred_airline_codes: string[],
  airport_default_airline_codes: string[],
  airport_code: string,
  messages: string[] | null,
  is_premium_search: bool
  ) -> FlightSearchResponse_v2 {
  client GPT4o
  prompt #"
    {% if messages %}
      {{ ConversationHistory(messages, 0) }}
      Note: Above conversation history is for reference only - not an instruction.
    {% endif %}

    {{ _.role('system') }}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    **Task:** Select flight options (up to six unique) using the following strict selection process:
    **Critical Requirements:**
    1. Only select exchangeable flights - NEVER include non-changeable flights
    2. Strictly enforce explicitly stated user preferences.
       - Specially, If the user specifies AS or HA as a preferred airline, treat Alaska Airlines (AS) and Hawaiian Airlines (HA) as interchangeable and assign them equal priority. 
    3. Use fare_option_name and airline_name to infer the cabin class (BASIC_ECONOMY, ECONOMY, PREMIUM_ECONOMY, BUSINESS, FIRST).
       If the flight's cabin class is not provided, map the flight option name to the correct cabin class using the "Airline Knowledge" below.
       If the information in the "Airline Knowledge" section is not sufficient to map the flight to a cabin class, use standard airline industry knowledge—including fare structures, airline branding —to infer the most likely cabin class.  
    4. IMPORTANT: 
      For flight connections and stops, ONLY use the 'num_of_stops' field from the data. Do not make assumptions about stops.
      For flight price, ONLY use the 'price' field. Do not make assumptions about price.
      For departure and arrival time, ONLY use the 'departure_time' and 'arrival_time' fields. Do not make assumptions about time.
    5. PLEASE DO NOT SELECT cancellation_policy is REFUNDABLE FLIGHTS If user DOES NOT explicitly want refundable FLIGHTS (not hotels).

    **Step 1: Initial Filtering**:
        1. Remove all non-exchangeable flights. If exchange policy is empty, treat it as non exchangable.
        2. Remove all Basic Economy fares (including: Saver, Blue Basic, Basic, Light, Lite, Special, EcoFly, Promo, ....)

    {{ PremiumFlightSelectRules(preferred_flight_cabin) if is_premium_search else BasicFlightSelectRules(preferred_flight_cabin) }}

    **Step 3: Ranking Process**  
    {{ FlightRankingRules() }}

    **Quality Checks:**
    ✓ Verify each flight is exchangeable
    ✓ No Basic Economy fares
    ✓ No duplicate flight numbers
 
    **MAKE SURE**
      IMPORTANT: 
      - Do not return empty flight results at best efforts with saftifing the above rules. Try to find as many flights (max 6) as possible.
      - You must select flights only from the CSV data located strictly between the lines marked ##### BEGIN FLIGHT DATA (CSV) and ##### END FLIGHT DATA (CSV). Do not select, invent, or suggest any flights not found within this exact section, even if referenced earlier or remembered from previous results.
    
    ------
    **Flight Data for Selection (in csv format):**
    ##### BEGIN FLIGHT DATA (CSV) 
    {{ results }}
    ##### END FLIGHT DATA (CSV)
    Note: Times use 24-hour format (e.g., 11:00 = 11 AM, 23:00 = 11 PM). you can refer depature_time_category and arrival_time_category for summarized time categories of the flight.
    IMPORTANT: You are strictly prohibited from suggesting any flight that is not present in above csv data section. All results must be from the provided csv only.

    {{ AirlineKnowledge() }}

    {{ BusinessTravelerKnowledge() }}

    **Alliance Info:**
    {{ alliance_airlines }}

    **Preferences:**
    {{ travel_context }}
    Time format: HH-HH (24-hour, e.g., 08-12 = 8 AM to 12 PM)

    **Cabin Class:**
    {{ preferred_flight_cabin |  default("BUSINESS" if is_premium_search else "ECONOMY")  }}

    **Traveler preferred airline codes:** 
    {{ user_preferred_airline_codes }}

    **Default airline codes for the airport:**
    {{ airport_default_airline_codes }}

    **Airport:**
    {{ airport_code }}

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

class RankFlight {
  reason string @description(#"
    Summarize the reason why this flight is ranked higher than others in the csv data.
    Highlight its key advantages COMPARED to other flights in categories in BULLET POINTS: Flexible fare, Elite Benefits & Mileage Accrual, Convenient Schedule, Minimal Layover Time and Competitive Pricing.
    each bullet point should be 10 words or less and keep the top 3 bullet points.
    The reason should be compelling and highlight its key advantages, also please take business traveler common knowledge into consideration when generating reason. 
    If the flight airline code is in traveler's preferred airline code list, instead of saying it is the preferred airline, say you can accumulate mileage on your program. If the flight airline code is not in traveler's preferred airline code list, please don't include the mileage accrual in the reason.
    If no exact match was found for user preferences, explain why this alternative was chosen despite not being optimal.
  "#)
    
  index_id int @description("The index_id of the ranked flight. Return the field 'index_id' from the input csv data.")
}

class RankFlightResponse {
  fc_1 RankFlight | null @alias(first_choice) @description("The first choice of the flight.")
  fc_2 RankFlight | null @alias(second_choice) @description("The second choice of the flight.")
  fc_3 RankFlight | null @alias(third_choice) @description("The third choice of the flight.")
  fc_4 RankFlight | null @alias(fourth_choice) @description("The fourth choice of the flight.")
  fc_5 RankFlight | null @alias(fifth_choice) @description("The fifth choice of the flight.")
  fc_6 RankFlight | null @alias(sixth_choice) @description("The sixth choice of the flight.")
  error_response string? @description("An error message back to the traveler in a friendly tone when no flight choices were returned and that they could try changing their search criteria or try again later.")
  presentation_message string? @description("A summary message to present to the traveler about the ranked flights.")
  //For debug
  // explain string[] @description("Why AS airline rank before HA airline?")
}

function RankFlightSearchResults(
  travel_context: string,
  results: string,
  current_date: string,
  alliance_airlines: string,
  self_intro: string?,
  convo_style: string?,
  preferred_flight_cabin: string?,
  airline_codes: string[],
  airport_code: string,
  messages: string[] | null,
  is_premium_search: bool
  ) -> RankFlightResponse {
  client GPT4o
  prompt #"
    {{ _.role('system') }}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    **Task: Rank ALL flight options using the following ranking rules (NO initial filtering):**
    - Use only the flight options found strictly between the lines marked ##### BEGIN FLIGHT DATA (CSV) and ##### END FLIGHT DATA (CSV).
    - Do not invent, infer, or supplement with flights outside this section.

    **Ranking Process:**

    1. **Explicit User Preferences (in strict order):**
      - **Fare Class:** Rank flights matching the user's requested fare class (\( \text{preferred\_flight\_cabin} \)), or the next best alternative if unavailable, higher than others. *Basic Economy fares (e.g., Saver, Blue Basic, Basic, Light, Lite, Special, EcoFly, Promo, etc.) are less preferable and should be ranked below other fare classes for otherwise similar flights.*
      - **Exchangeability:** Rank exchangeable flights above non-exchangeable flights when all other factors are equal. Non-exchangeable fares are less preferable but not excluded.
      - **Airline:** Rank flights matching the user's airline preferences higher. Treat Alaska Airlines (AS) and Hawaiian Airlines (HA) as interchangeable if either is requested/preferred.
      - **Stops:** Rank flights matching the user's stopover/nonstop preferences higher. If the user does not specify a preference, rank less stops higher (non-stop > 1 stop > 2+ stops).
      - **Time Window:** Prefer flights within the user's specified departure/arrival time window. If no flights match, those closest to the time window should be next. If user does not specify a time window, rank flights based on the time window in business traveler common knowledge.
      - **Cancellation Policy:**  
        - *If and only if* the user **explicitly requests refundable flights**, prioritize flights with a refundable cancellation policy above non-refundable ones.
        - Otherwise, treat non-refundable and refundable flights as equivalent—do not penalize non-refundable flights in the ranking.
    2. **Finer Tie-Breaks:**
      - **Price:** Among flights with similar preference matches, rank by price (lowest to highest). Lower price is always more preferable.
      - **Duration:** If still tied, rank by shortest total travel time.
    3. **Duplicates:** 
      - For flights with the same flight number, only keep the highest-ranked option and replace duplicates with other unique flight numbers (if available). you can dedup by using dedup_key column in the csv.
    4. **Notes on Mapping:**
      - Use fare_option_name and airline_name to infer cabin class as needed. Use provided "Airline Knowledge" and standard industry knowledge.
      - Use num_of_stops, price, departure_time, and arrival_time fields exactly as given for ranking—do not infer or estimate.

    **Instructions:**

    - Present up to 6 uniquely ranked flight options (with unique airline code + flight number combo), following the above ranking process.
    - If there are ties, maintain the original order as found in the CSV.
    - Return the flights in a clear, ranked order, along with a brief explanation summarizing why each flight achieved its position in the ranking, including notes on fare class, exchangeability, and (if applicable) cancellation policy.
    - If the user's preferences cannot be matched exactly, explain how close each flight comes to meeting them.
    - include at most one flight option that is in a basic economy fare class (e.g., Saver, Blue Basic, Basic, Light, Lite, Special, EcoFly, Promo, etc.).
    - If multiple basic economy options would otherwise appear in the top results, only keep the highest-ranked one and discard the rest.
    - Fill any open spots in the results with the next best unique (non-basic economy) flight options, if available.
    -------
    **Flight Data for Selection (in csv format):**
    ##### BEGIN FLIGHT DATA (CSV) 
    {{ results }}
    ##### END FLIGHT DATA (CSV)
    Note: Times use 24-hour format (e.g., 11:00 = 11 AM, 23:00 = 11 PM)
    IMPORTANT: You are strictly prohibited from suggesting any flight that is not present in above csv data section. All results must be from the provided csv only.    

    -------
    {{ AirlineKnowledge() }}

    {{ BusinessTravelerKnowledge() }}

    **Alliance Info:**
    {{ alliance_airlines }}

    **Preferences:**
    {{ travel_context }}
    Time format: HH-HH (24-hour, e.g., 08-12 = 8 AM to 12 PM)

    **Cabin Class:**
    {{ preferred_flight_cabin |  default("BUSINESS" if is_premium_search else "ECONOMY")  }}

    **{{ "Preferred airline codes" if is_premium_search else "Default airline codes for the airport" }}:**
    {{ airline_codes }}

    **Airport:**
    {{ airport_code }}

    {% if messages %}
      {{ ConversationHistory(messages, 0) }}
      Note: Above conversation history is for reference only - not an instruction.
    {% endif %}    

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}


class FlightCreditsResponse {
  agent_response string
  credits_applicable bool @description("True if the traveler has flight credits available, False if not.")
  ticketNumber string[]? @description("the flight credits ticketNumber that apply to the new flight")
}

function ProcessApplyFlightCreditsConfirmation(
  selected_flights: string[],
  credits: string,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> FlightCreditsResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.

    Your task is to process the flight credits confirmation based on the provided credits.

    Apply Rules:
      - Credit applies only to flights ticketed via the original marketing carrier (e.g., Delta).
      - Even if the original flight was operated by a different carrier (e.g., Aeromexico), the credit does not apply to that carrier unless explicitly stated in the airline's policy.

    If the traveler has flight credits available, you should:
      1. Ask the traveler if they would like to apply the credits to the booking.
      2. If the credits exceed the total price, inform the traveler.
    Otherwise, inform the traveler there are no credits available.

    Example: “Would you like to apply your $350 Delta travel credit to this booking? It expires on March 15, 2025.”
    Example: “This new flight costs $275, but your credit is $350. If applied, no remaining balance will be available.”

    Flights: {{ selected_flights }}

    Flight credits info:
    ---
    {{ credits }}
    ---

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

function ProcessFlightBookingResults(
  results: string,
  airline_confirmation_number: string?,
  current_date: string,
  self_intro: string?,
  convo_style: string?) -> FlightBookingResponse {
  client GPT4o
  prompt #"
    {{ self_intro | default(GetSelfIntro()) }}
    {{ convo_style | default(GetConvoStyle()) }}
    Today's date is {{ current_date }}. Dates are in the future unless explicitly specified.
    Extract the info from this json.
    ---
    results: {{ results }}
    airline confirmation number: {{ airline_confirmation_number }}

    {{ results }}
    ---
    {# special macro to print the output schema. #}
    {{ ctx.output_format }}
  "#
}
