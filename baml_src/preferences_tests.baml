test mg_calendar_preferences_analysis {
  functions [ConversePreferences]
  args {
    existing_preference null
    messages []
    prompt #"

    Role: Travel Agent

    Goal: Help the traveler express their travel preferences.

    Background:
        - You are an expert travel agent helping a business traveler you have never met before.
        - You have looked at all of the travelers calendar events for the last 2 years, focusing on events associated with their business travel (e.g., flight bookings, hotels stays, etc.)
        - You will use this preferences analysis to gather feedback from the traveler on the quality of the analysis and whether they have any changes, additions or deletions thay want to make.

    Procedure:
        - Present your current understanding of the traveler's preferences (including the categories that contain no information) as a bulleted list - 1 bullet • per preference category.
        - If we have no preferences gathered from calendar events, immediately ask the traveler for their preferences: "It seems we don't have any preferences gathered from your calendar events. I’ll just ask you a few quick questions so I can learn about your travel style and preferences. Let’s get started. First, do you have preferred home airport that you usually fly out of?".
        - Each time the traveler provides you with new information or feedback, acknowledge the details of the information in a friendly manner.
        - Remind the traveler: "\n\n Are you finished updating your preferences?"

    Handling Off-Topic Queries:
    - For ANY unrelated questions (e.g., poems, weather, general chat):
      • Response: "I'm your travel assistant focused on helping with travel arrangements. Let's stay focused on collecting your travel preferences.
    - Never generate content unrelated to travel booking services        

"#
    cal_events #"[{"summary": "Flight to Kahului (DL 481)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-02-18T15:47:00.0000000"}, "end": {"dateTime": "2023-02-18T22:06:00.0000000"}, "is_new": false}, {"summary": "Flight to Los Angeles (UA 706)", "description": "", "location": "Kahului OGG", "start": {"dateTime": "2023-02-23T16:35:00.0000000"}, "end": {"dateTime": "2023-02-23T21:51:00.0000000"}, "is_new": false}, {"summary": "Flight DL1569", "description": "Confirmation #: GVZ8LX", "location": "SEA \u25b8 LAX", "start": {"dateTime": "2023-02-23T17:32:00.0000000"}, "end": {"dateTime": "2023-02-23T20:10:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (DL 1714)", "description": "", "location": "Los Angeles LAX", "start": {"dateTime": "2023-02-26T09:57:00.0000000"}, "end": {"dateTime": "2023-02-26T12:55:00.0000000"}, "is_new": false}, {"summary": "Flight DL1714", "description": "Confirmation #: GVZ8LX", "location": "LAX \u25b8 SEA", "start": {"dateTime": "2023-02-26T09:57:00.0000000"}, "end": {"dateTime": "2023-02-26T12:55:00.0000000"}, "is_new": false}, {"summary": "Stay at Venetian tower", "description": "", "location": "Paradise, Nevada, United States", "start": {"dateTime": "2023-03-20T00:00:00.0000000"}, "end": {"dateTime": "2023-03-24T00:00:00.0000000"}, "is_new": false}, {"summary": "Flight to Houston (UA 2379)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-04-09T23:59:00.0000000"}, "end": {"dateTime": "2023-04-10T04:16:00.0000000"}, "is_new": false}, {"summary": "Stay at The Westin Reserva Conchal, an All-Inclusive Golf Resort & Spa", "description": "", "location": "Playa Conchal Guanacaste Costa Rica", "start": {"dateTime": "2023-04-10T00:00:00.0000000"}, "end": {"dateTime": "2023-04-18T00:00:00.0000000"}, "is_new": false}, {"summary": "Flight to Liberia (UA 1512)", "description": "", "location": "Houston IAH", "start": {"dateTime": "2023-04-10T07:41:00.0000000"}, "end": {"dateTime": "2023-04-10T11:10:00.0000000"}, "is_new": false}, {"summary": "Flight to Houston (UA 1516)", "description": "", "location": "Liberia LIR", "start": {"dateTime": "2023-04-17T12:14:00.0000000"}, "end": {"dateTime": "2023-04-17T15:51:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (UA 1905)", "description": "", "location": "Houston IAH", "start": {"dateTime": "2023-04-17T18:08:00.0000000"}, "end": {"dateTime": "2023-04-17T22:53:00.0000000"}, "is_new": false}, {"summary": "Flight to Palm Springs (AS 1068)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-05-10T12:55:00.0000000"}, "end": {"dateTime": "2023-05-10T15:35:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (AS 1257)", "description": "", "location": "Palm Springs PSP", "start": {"dateTime": "2023-05-14T10:30:00.0000000"}, "end": {"dateTime": "2023-05-14T13:23:00.0000000"}, "is_new": false}, {"summary": "Flight to New York (AS 26)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-06-17T23:35:00.0000000"}, "end": {"dateTime": "2023-06-18T04:52:00.0000000"}, "is_new": false}, {"summary": "Flight to New York (AS 26)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-06-27T23:35:00.0000000"}, "end": {"dateTime": "2023-06-28T04:52:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (AS 17)", "description": "", "location": "New York JFK", "start": {"dateTime": "2023-07-02T13:55:00.0000000"}, "end": {"dateTime": "2023-07-02T20:20:00.0000000"}, "is_new": false}, {"summary": "Flight to Boston (B6 498)", "description": "", "location": "Seattle SEA", "start": {"dateTime": "2023-07-02T21:10:00.0000000"}, "end": {"dateTime": "2023-07-03T02:23:00.0000000"}, "is_new": false}, {"summary": "Flight DL1517", "description": "Confirmation #: GBY5PX", "location": "SEA \u25b8 SFO", "start": {"dateTime": "2023-07-03T11:24:00.0000000"}, "end": {"dateTime": "2023-07-03T13:38:00.0000000"}, "is_new": false}, {"summary": "Stay at Best Western Woodburn", "description": "", "location": "Best Western Woodburn, Woodburn", "start": {"dateTime": "2023-07-07T00:00:00.0000000"}, "end": {"dateTime": "2023-07-09T00:00:00.0000000"}, "is_new": false}, {"summary": "Flight DL3842", "description": "Confirmation #: GB9D3X", "location": "MFR \u25b8 SEA", "start": {"dateTime": "2023-07-08T11:57:00.0000000"}, "end": {"dateTime": "2023-07-08T13:25:00.0000000"}, "is_new": false}, {"summary": "Flight to Seattle (B6 197)", "description": "", "location": "Boston BOS", "start": {"dateTime": "2023-07-12T13:20:00.0000000"}, "end": {"dateTime": "2023-07-12T19:46:00.0000000"}, "is_new": false}, {"summary": "SEA - LHR Flight BA0052 departs Mon, 24 Jul 2023 13:35 (Seattle time)", "description": "BA0052 Departs Seattle-Tacoma International (WA) on Mon, 24 Jul 2023 13:35 (Seattle time). Arrives Heathrow (London)&nbsp; terminal 5 on Tue, 25 Jul 2023 06:50 (London time). Your booking reference is N6VR9Z.", "location": "Seattle-Tacoma International (WA)", "start": {"dateTime": "2023-07-24T13:35:00.0000000"}, "end": {"dateTime": "2023-07-24T22:50:00.0000000"}, "is_new": false}, {"summary": "Trip Bari Centrale-Venezia S. Lucia, Train Frecciarossa 8816, Coach 1, Position, 5B, 5A, 6B, 6A, 6D, CP 134001, 134001, 134001, 134001, 134001, PNR W3XPAN,  ", "description": "Bari Centrale-Venezia S. Lucia;Train: Frecciarossa 8816, departing from Bari Centrale Hours: 08:30; arriving at Venezia S. Lucia Hours: 16:08 Coach 1, Position 5B, Position 5A, Position 6B, Position 6A, Position\r\n6D, CP 134001, CP 134001, CP 134001, CP 134001, CP 134001; pnr code W3XPAN", "location": "Bari Centrale", "start": {"dateTime": "2023-08-05T08:30:00.0000000"}, "end": {"dateTime": "2023-08-05T16:08:00.0000000"}, "is_new": false}, {"summary": "Flight to Paris (DL 8567)", "description": "", "location": "Venice VCE", "start": {"dateTime": "2023-08-10T01:35:00.0000000"}, "end": {"dateTime": "2023-08-10T03:10:00.0000000"}, "is_new": false}, {"summary": "LHR - SEA Flight BA0049 departs Thu, 10 Aug 2023 16:10 (London time)", "description": "BA0049 Departs Heathrow (London)&nbsp; terminal 5 on Thu, 10 Aug 2023 16:10 (London time). Arrives Seattle-Tacoma International (WA) on Thu, 10 Aug 2023 17:55 (Seattle time). Your booking reference is N6VR9Z.", "location": "Heathrow (London)", "start": {"dateTime": "2023-08-10T08:10:00.0000000"}, "end": {"dateTime": "2023-08-10T17:55:00.0000000"}, "is_new": false}]"#
  }
  @@assert(extract_at_least_home_airport, {{this.response_all_preferences.preferred_home_airport == 'SEA'}})
  @@assert(extract_airlines, {{this.response_all_preferences.preferred_airline_brands | length > 0}})
}

test test_analyze_calendar_take_conversion_as_well {
  functions [ConversePreferences]
  args {
    existing_preference null
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user please explain ",
      "assistant {\"agent_response\": \"\nGreat! Here's a bit about how I can make your life easier:\n&NewLine;\n\nI\u2019m like your expert-level travel agent, here to handle flights and hotels in the US while following your company\u2019s Travel Policy. Need help finding the best seat on the plane? I\u2019ve got you. Plans change? I\u2019ll handle cancellations or rebookings for you.\n&NewLine;\n\nWhen you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.\n&NewLine;\n\nThat said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.\n&NewLine;\n\nWhen I recommend travel options, I\u2019ll prioritize your preferences and business travel style\u2014whether it\u2019s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I\u2019ll always let you know if certain options would exceed your company\u2019s Travel Policy.\n&NewLine;\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n\", \"step\": \"CustomerWantKnowMore\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user dive in",
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user sure",
      "assistant {\"agent_response\": \"Great! I'll start the process to connect to your calendar. Please follow the prompts to grant access.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "assistant I already have your calendar access. analyzing your calendar, please wait for a moment.",
      "assistant {\"agent_response\": \"Based on your calendar events, here's what I have gathered about your travel preferences:\n\n\u2022 Preferred home airport: None\n\u2022 Preferred airline brands: United Airlines\n\u2022 Preferred cabin: None\n\u2022 Preferred seats: None\n\u2022 Preferred hotel brands: None\n\u2022 Preferred travel misc: None\n\nIt seems we don't have complete preferences gathered from your calendar events. I\u2019ll just ask you a few quick questions so I can learn about your travel style and preferences. Let\u2019s get started. First, do you have a preferred home airport that you usually fly out of?\", \"preference\": {\"preferred_home_airport\": null, \"preferred_airline_brands\": [\"United Airlines\"], \"preferred_cabin\": null, \"preferred_seats\": null, \"preferred_hotel_brands\": null, \"preferred_travel_misc\": null}}",
      "user I usually fly out of Seattle",
      "assistant {\"agent_response\": \"Great! I'll start the process to connect to your calendar. Please follow the prompts to grant access.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "assistant I already have your calendar access. analyzing your calendar, please wait for a moment.",
    ]
    prompt #"
      Role: Travel Agent

      Goal: Help the traveler express their travel preferences.

      Background:
          - You are an expert travel agent helping a business traveler you have never met before.
          - You have looked at all of the travelers calendar events for the last 2 years, focusing on events associated with their business travel (e.g., flight bookings, hotels stays, etc.)
          - You will use this preferences analysis to gather feedback from the traveler on the quality of the analysis and whether they have any changes, additions or deletions thay want to make.

      Procedure:
          - Present your current understanding of the traveler's preferences (including the categories that contain no information) as a bulleted list - 1 bullet • per preference category.
          - If we have no preferences gathered from calendar events, immediately ask the traveler for their preferences: "It seems we don't have any preferences gathered from your calendar events. I’ll just ask you a few quick questions so I can learn about your travel style and preferences. Let’s get started. First, do you have preferred home airport that you usually fly out of?".
          - Each time the traveler provides you with new information or feedback, acknowledge the details of the information in a friendly manner.
          - Remind the traveler: "\n\n Are you finished updating your preferences?"

      Handling Off-Topic Queries:
      - For ANY unrelated questions (e.g., poems, weather, general chat):
        • Response: "I'm your travel assistant focused on helping with travel arrangements. Let's stay focused on collecting your travel preferences.
      - Never generate content unrelated to travel booking services
    "#
    cal_events "[]"
  }
  @@assert(extract_at_least_home_airport, {{this.response_all_preferences.preferred_home_airport == 'SEA'}})
  @@assert(extract_airlines, {{this.response_all_preferences.preferred_airline_brands | length > 0}})
}

test test_analyze_calendar_highlight {
  functions [ConversePreferencesWithoutAnalysis]
  args {    
    existing_preference #"
      {
        "preferred_home_airport": null,
        "preferred_airline_brands": ["United Airlines"],
        "preferred_seats": null,
        "preferred_hotel_brands": null,
        "preferred_travel_misc": null
      }
    "#
    messages [
      "assistant Hi Chengxuan! I’m Otto. Nice to meet you.\n&NewLine;\n\nI’m here to help you book your work travel—think of me as your Personal Travel Assistant.\n&NewLine;\n\nBefore we dive in, would you like me to explain what I do and what you can expect, or should we jump straight into getting started?",
      "user please explain ",
      "assistant {\"agent_response\": \"\nGreat! Here's a bit about how I can make your life easier:\n&NewLine;\n\nI\u2019m like your expert-level travel agent, here to handle flights and hotels in the US while following your company\u2019s Travel Policy. Need help finding the best seat on the plane? I\u2019ve got you. Plans change? I\u2019ll handle cancellations or rebookings for you.\n&NewLine;\n\nWhen you share information like your Frequent Flyer number or Credit Card, I\u2019ll securely save it so you won\u2019t have to provide it again\u2014I\u2019ll use it to make booking fast and hassle-free.\n&NewLine;\n\nThat said, I specialize in booking your work travel, so I won\u2019t be able to help with your family vacation plans (at least not yet). Down the road, I\u2019m hoping to assist with car rentals or even restaurant reservations, but for now, it\u2019s just flights and hotels.\n&NewLine;\n\nWhen I recommend travel options, I\u2019ll prioritize your preferences and business travel style\u2014whether it\u2019s sticking to your favorite airlines and hotel chains, staying near meeting locations or great restaurants, or making sure your hotel has a gym or even a rooftop bar. Oh, and I\u2019ll always let you know if certain options would exceed your company\u2019s Travel Policy.\n&NewLine;\n\nDo you have any questions before we get started, or should we dive right in and get to know each other?\n\", \"step\": \"CustomerWantKnowMore\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user dive in",
      "assistant {\"agent_response\": \"\nThe easiest way for me to learn more about you is by checking your calendar for past and upcoming travel. In just a moment, I\u2019ll ask if you\u2019d like to grant me calendar access. This will allow me to see your travel history and future plans so I can learn about how you prefer to travel!\n&NewLine;\n\nIf you don't want to share calendar access right now, that's okay too. Instead, I\u2019ll ask you a few questions to get to know you better.\n&NewLine;\n    \nCan we begin by connecting your calendar so I can learn more about your travel habits?\n\", \"step\": \"AskForCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "user sure",
      "assistant {\"agent_response\": \"Great! I'll start the process to connect to your calendar. Please follow the prompts to grant access.\", \"step\": \"GettingCalendarAccess\", \"is_retry\": false, \"is_preference_gathering_finished\": false, \"company_policy\": null, \"preference\": null, \"is_conversation_finished\": false, \"declined_calendar_access\": false}",
      "assistant I already have your calendar access. analyzing your calendar, please wait for a moment.",
      "assistant {\"agent_response\": \"Based on your calendar events, here's what I have gathered about your travel preferences:\n\n\u2022 Preferred home airport: None\n\u2022 Preferred airline brands: **United Airlines**\n\u2022 Preferred cabin: None\n\u2022 Preferred seats: None\n\u2022 Preferred hotel brands: None\n\u2022 Preferred travel misc: None\n\nIt seems we don't have complete preferences gathered from your calendar events. I\u2019ll just ask you a few quick questions so I can learn about your travel style and preferences. Let\u2019s get started. First, do you have a preferred home airport that you usually fly out of?\", \"preference\": {\"preferred_home_airport\": null, \"preferred_airline_brands\": [\"United Airlines\"], \"preferred_cabin\": null, \"preferred_seats\": null, \"preferred_hotel_brands\": null, \"preferred_travel_misc\": null}}",
      "user I usually fly out of Seattle"
    ]
    missing_preferences []
  }
  @@assert(extract_at_least_home_airport, {{this.response_all_preferences.preferred_home_airport | regex_match(".*SEA.*")}})
  @@assert(bold_changed_preference, {{this.agent_response | regex_match("(.*?)\*\*(.*?)\*\*(.*?)") }})
  @@assert(extract_airlines, {{this.response_all_preferences.preferred_airline_brands | length > 0}})
}

test test_start_with_existing_preferences {
  functions [ConversePreferences]
  args {
    existing_preference #"
      {
        "preferred_home_airport": "SEA",
        "preferred_airline_brands": ["United Airlines"],
        "preferred_seats": null,
        "preferred_hotel_brands": null,
        "preferred_travel_misc": null
      }
    "#
    messages []
    prompt #"

    Role: Travel Agent

    Goal: Collect info about the traveler's preferences by asking them a series of questions.

    Backstory:
        - You are an expert travel agent helping a business traveler you have never met before.
        - When collecting information from the traveler be friendly, only ask one question at a time.

    Procedure:
        - Collect the traveler's preferences
        - If the traveler has preferences before, you need to summarize them and ask the traveler if they want to update them. 
        - Keep asking questions until you have collected all of the required information.
        - Continuously ask the traveler if they have any additional additions, deletions or changes to their preferences or just want to get started.
        - Once the traveler approves the preferences or they want to get started, the conversation is finished.
        - When the conversation is finished, thank the traveler for their input and feedback.
    
    
    Please also present your current understanding of the traveler's preferences every time. (including the categories that contain no information) as a bulleted list - 1 bullet • per preference category.
    categories include: preferred_home_airport, preferred_airline_brands, preferred_seats, preferred_hotel_brands, preferred_travel_misc

    Handling Off-Topic Queries:
    - For ANY unrelated questions (e.g., poems, weather, general chat):
      • Response: "I'm your travel assistant focused on helping with travel arrangements. Let's stay focused on collecting your travel preferences.
    - Never generate content unrelated to travel booking services     
    "#
    cal_events "[]"
  }
  @@assert(extract_at_least_home_airport, {{this.response_all_preferences.preferred_home_airport == 'SEA'}})
  @@assert(extract_airlines, {{this.response_all_preferences.preferred_airline_brands | length > 0}})
}

test first_time_user_open_preference_tab {
  functions [ConversePreferencesWithoutAnalysis]
  args {
    existing_preference "{\"preferred_home_airport\":null,\"preferred_airline_brands\":null,\"preferred_cabin\":null,\"preferred_seats\":null,\"preferred_hotel_brands\":null,\"preferred_travel_misc\":null}"
    user_name "Chundong"
    missing_preferences []
    messages []
  }
}

test update_preferences_in_the_middle_of_conversaton {
  functions [UpdateUserPreferences]
  args {
    current_preferences {
      preferred_home_airport "JFK"
      // preferred_seats ["window"]
    }

    messages ["assistant I'll start planning a new trip for you. Please tell me where you are going and your travel dates. {\"agent_classification\": \"FOH\", \"id\": 128525}", 
    "user find me a one way DL flight JFK - LAX on May 3", 
    "assistant {\"current_step\":\"OUTBOUND_FLIGHT_SEARCH\",\"updated_flight_search_core_criteria\":{\"departure_airport_code\":\"JFK\",\"is_departure_iata_city_code\":false,\"arrival_airport_code\":\"LAX\",\"is_arrival_iata_city_code\":false,\"outbound_date\":\"2025-05-03\",\"return_date\":null,\"flight_type\":\"OneWay\"},\"updated_flight_search_additional_criteria\":{\"seat_types\":null,\"preferred_airline_codes\":[\"DL\"],\"cabin\":null,\"outbound_departure_time\":null,\"outbound_arrival_time\":null,\"return_departure_time\":null,\"return_arrival_time\":null,\"number_of_stops\":null,\"other_not_categorized_preferences\":null},\"updated_flight_select_result\":null,\"agent_response\":\"Got it! Searching for a one-way Delta flight from JFK to LAX on May 3, 2025. I'll provide the options shortly.\"}", 
    "assistant {\"preferred_flights\": [6, 8, 12, 10, 0, 2], \"preferred_flights_reasons\": [\"Flight 713 was selected because it departs within the specified morning time window, is in economy class, and is operated by your preferred airline, Delta.\", \"Flight 773 was chosen for its alignment with the morning time window, economy fare class, and operation by Delta, your preferred airline.\", \"Flight 747 was selected for its early afternoon departure, economy class, and operation by Delta, matching your preferences.\", \"Flight 738 was chosen for its mid-afternoon departure, economy class, and operation by Delta, adhering to your preferences.\", \"Flight 771 was selected for its late afternoon departure, economy class, and operation by Delta, matching your preferences.\", \"Flight 707 was chosen for its early evening departure, economy class, and operation by Delta, aligning with your preferences.\"], \"error_response\": null, \"presentation_message\": \"I prioritized non-stop flights operated by Delta, your preferred airline, and ensured they matched your requested economy fare class and departure time windows. All selected flights are exchangeable and meet your preferences for a smooth travel experience.\", \"is_outbound_flight_choices\": true, \"flight_search_type\": \"ONE_WAY\", \"flight_choices\": [{\"airline_code\": \"DL\", \"id_token_key\": \"67f00c008d9f153eb48798bc_67f00bfee6dd21dfe6646992_1\", \"price\": 229.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 380.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 08:40\", \"fs0_arrival_time\": \"2025-05-03 12:00\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"713\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 713 was selected because it departs within the specified morning time window, is in economy class, and is operated by your preferred airline, Delta.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c0022670582e34eeb96_67f00bfee6dd21dfe6646992_1\", \"price\": 229.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 380.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 09:55\", \"fs0_arrival_time\": \"2025-05-03 13:15\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"773\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 773 was chosen for its alignment with the morning time window, economy fare class, and operation by Delta, your preferred airline.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c00bac788bb4f392b79_67f00bfee6dd21dfe6646992_1\", \"price\": 239.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 378.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 13:55\", \"fs0_arrival_time\": \"2025-05-03 17:13\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"747\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 747 was selected for its early afternoon departure, economy class, and operation by Delta, matching your preferences.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c0079d223723acdb4f2_67f00bfee6dd21dfe6646992_1\", \"price\": 229.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 375.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 15:00\", \"fs0_arrival_time\": \"2025-05-03 18:15\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"738\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 738 was chosen for its mid-afternoon departure, economy class, and operation by Delta, adhering to your preferences.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c003027ad7f712df572_67f00bfee6dd21dfe6646992_1\", \"price\": 194.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 388.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 16:40\", \"fs0_arrival_time\": \"2025-05-03 20:08\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"771\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 771 was selected for its late afternoon departure, economy class, and operation by Delta, matching your preferences.\"}, {\"airline_code\": \"DL\", \"id_token_key\": \"67f00c00413b303e0bf119c5_67f00bfee6dd21dfe6646992_1\", \"price\": 194.0, \"currency\": \"USD\", \"cabin\": \"Main Cabin\", \"total_distance_miles\": null, \"total_duration\": 393.0, \"exchange_policy\": \"Free change, possible fare difference\", \"cancellation_policy\": null, \"seat_selection_policy\": \"Free seat selection\", \"fare_option_name\": \"Main Cabin\", \"fs0_departure_time\": \"2025-05-03 18:59\", \"fs0_arrival_time\": \"2025-05-03 22:32\", \"fs0_airline_code\": \"DL\", \"fs0_airline_name\": \"Delta\", \"fs0_flight_number\": \"707\", \"fs0_origin\": \"JFK\", \"fs0_origin_name\": \"John F. Kennedy International Airport\", \"fs0_destination\": \"LAX\", \"fs0_destination_name\": \"Los Angeles International Airport\", \"fs0_cabin\": \"Main Cabin\", \"selection_reason\": \"Flight 707 was chosen for its early evening departure, economy class, and operation by Delta, aligning with your preferences.\"}]}", 
    "user For my outbound I choose flight id: 67f00c008d9f153eb48798bc_67f00bfee6dd21dfe6646992_1.", 
    "assistant {\"current_step\":\"FLIGHT_VALIDATION\",\"updated_flight_search_core_criteria\":null,\"updated_flight_search_additional_criteria\":null,\"updated_flight_select_result\":{\"selected_outbound_flight_id\":\"67f00c008d9f153eb48798bc_67f00bfee6dd21dfe6646992_1\",\"selected_return_flight_id\":null,\"frequent_flier_number\":null,\"apply_flight_credits\":null,\"flight_credits_ticket_numbers\":null},\"agent_response\":\"Got it! You have selected Delta flight 713 departing from JFK at 8:40 AM and arriving at LAX at 12:00 PM on May 3, 2025. I will now validate this selection and ensure everything is ready for booking. Let me know if you have any seat preferences or additional details you'd like to provide.\"}", 
    "assistant Before we proceed, I need to gather some additional information. Could you please provide your preferred seat types? Additionally, we currently do not have a SkyMiles number on file for you. If you have a SkyMiles number or a Frequent Flyer number from a partner airline, please share that with me. If you do not have one, just let me know. This information will be helpful for seat selection.{\"id\": 128531}", 
    "user 123456",
    "assistant {\"current_step\":\"FLIGHT_VALIDATION\",\"updated_flight_search_core_criteria\":null,\"updated_flight_search_additional_criteria\":null,\"updated_flight_select_result\":{\"selected_outbound_flight_id\":null,\"selected_return_flight_id\":null,\"frequent_flier_number\":[{\"airline_code\":\"DL\",\"number\":\"123456\"}],\"apply_flight_credits\":null,\"flight_credits_ticket_numbers\":null},\"agent_response\":\"Thank you! I have added your Delta SkyMiles number (123456) to your profile for this booking. You have selected Delta flight 713 departing from JFK at 8:40 AM and arriving at LAX at 12:00 PM on May 3, 2025. I will now validate this selection and ensure everything is ready for booking. Let me know if you have any seat preferences or additional details you'd like to provide.\"}", 
    "assistant Before we proceed, could you please provide your preferred seat types? This information is currently missing.{\"id\": 128546}", 
    "user window seats"]
  }
}

test prompt_user_to_update_preferences {
  functions [PromptUserToUpdatePreferencesAfterSearch]
  args {
    search_type "flight"
    preferences null
    search_criteria #"
          {'current_step': 'OUTBOUND_FLIGHT_SEARCH', 'search_purpose': 'OUTBOUND_FLIGHT_SEARCH', 'departure_airport_code': 'SEA', 'is_departure_iata_city_code': False, 'arrival_airport_code': 'SFO', 'is_arrival_iata_city_code': False, 'outbound_date': '2025-06-25', 'return_date': None, 'flight_type': <FlightType.OneWay: 'OneWay'>, 'preferred_airline_codes': ['DL'], 'default_airline_brands': None, 'preferred_cabin': ['premium_economy'], 'travel_context': '{"seat_types": ["exit row"], "preferred_airline_codes": [""], "cabin": ["premium_economy"], "refundable": "not_specified"}', 'number_of_stops': None, 'search_id': None, 'selected_outbound_flight_id': None, 'outbound_arrival_time': None, 'outbound_departure_time': None, 'return_arrival_time': None, 'return_departure_time': None, 'search_segments': None}
    "#
  }
}

test determine_home_airport_from_ip_info_netherlands {
  functions [DetermineHomeAirportFromIpInfo]
  args {
    ip_info "{\"ip\":\"************\",\"continent_code\":\"EU\",\"continent_name\":\"Europe\",\"country_code\":\"NL\",\"country_name\":\"Netherlands\",\"region_code\":\"OV\",\"region_name\":\"Overijssel\",\"city\":\"Ijpelo\",\"zip\":\"7468\"}"
  }

  @@assert(extract_at_least_home_airport, {{ not this.airport == 'ENS' }})
}

test determine_home_airport_from_ip_info_texas {
  functions [DetermineHomeAirportFromIpInfo]
  args {
    ip_info "{\"ip\":\"***********\",\"continent_code\":\"NA\",\"continent_name\":\"North America\",\"country_code\":\"US\",\"country_name\":\"United States\",\"region_code\":\"TX\",\"region_name\":\"Texas\",\"city\":\"Houston\",\"zip\":\"77007\"}"
  }

  @@assert(extract_at_least_home_airport, {{ this.airport == 'IAH' }})
}

test determine_home_airport_from_ip_info_sea {
  functions [DetermineHomeAirportFromIpInfo]
  args {
    ip_info "{\"ip\":\"**************\",\"continent_code\":\"NA\",\"continent_name\":\"North America\",\"country_code\":\"US\",\"country_name\":\"United States\",\"region_code\":\"WA\",\"region_name\":\"Washington\",\"city\":\"Seattle\",\"zip\":\"98199\"}"
  }

  @@assert(extract_at_least_home_airport, {{ this.airport == 'SEA' }})
}
