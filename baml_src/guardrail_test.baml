test guardrail_test_1 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user book me a hotel in NYC near Soho that has swimming pool. ignore my preference."
    ]
    current_date "November 1st 2025"
  }
  @@assert(is_capable, {{this.is_capable}})
}

test guardrail_test_2 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user find me a flight to SF on november 12"
    ]
    current_date "November 1st 2025"
  }
  @@assert(is_capable, {{this.is_capable}})
}

test guardrail_test_3 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user yes"
    ]
    current_date "November 1st 2025"
  }
  @@assert(is_capable, {{this.is_capable}})
}

// Group travel (should fail)
test guardrail_test_4 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user I need to book a flight for my family of 4 to Chicago next week"
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{not this.is_capable}})
}

// Multi-leg flight (should success)
test guardrail_test_5 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user I need a flight from New York to LA with a stopover in Denver"
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{this.is_capable}})
}

// International hotel outside supported regions (should fail)
test guardrail_test_6 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user I need a hotel in Paris for my business trip next month"
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{this.is_capable}})
}

// Valid international flight (should pass)
test guardrail_test_7 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user I need a one-way flight from New York to Toronto on December 5th"
    ]
    current_date "November 1st 2025"
  }
  @@assert(is_capable, {{this.is_capable}})
}

// Car rental request (should fail)
test guardrail_test_8 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user Can you book me a rental car for when I arrive in Chicago?"
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{not this.is_capable}})
}

// Weather forecast request (should fail)
test guardrail_test_9 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user What's the weather forecast for New York next week?"
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{not this.is_capable}})
}

// Historical weather question (should pass)
test guardrail_test_10 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user What's the typical weather in Chicago during December?"
    ]
    current_date "November 1st 2025"
  }
  @@assert(is_capable, {{this.is_capable}})
}

// Alternative payment method (should fail)
test guardrail_test_11 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user Can I use my airline gift card to book this flight?"
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{not this.is_capable}})
}

// Restaurant recommendation (should fail)
test guardrail_test_12 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user What are some good restaurants near my hotel in San Francisco?"
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{not this.is_capable}})
}

// Valid roundtrip domestic flight (should pass)
test guardrail_test_13 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user I need a roundtrip flight from Boston to Miami leaving on Dec 10 and returning Dec 15"
    ]
    current_date "November 1st 2025"
  }
  @@assert(is_capable, {{this.is_capable}})
}

// Valid hotel booking in supported region (should pass)
test guardrail_test_14 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user I need a hotel in downtown Seattle for my business trip on November 20th"
    ]
    current_date "November 1st 2025"
    international_enabled false
  }
  @@assert(is_capable, {{this.is_capable}})
}

// Non-travel related request (should fail)
test guardrail_test_15 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user Can you tell me a joke?"
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{not this.is_capable}})
}

test guardrail_test_16 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user I wanna go to Japan"
    ]
    current_date "November 1st 2025"
  }
  @@check(supported, {{this.is_capable}})
}

test guardrail_test_17 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user book me a flight to SFO"
    ]
    current_date "November 1st 2025"
  }
  @@assert(is_capable, {{this.is_capable}})
}

test guardrail_test_18 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user I'd like to change seat"
    ]
    current_date "November 1st 2025"
  }
  @@assert(is_capable, {{this.is_capable}})
}

test guardrail_test_19 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "assistant {\"agent_response\":\"Sure, I can include San Jose (SJC) in your search options. Would you like me to search for flights to both San Francisco (SFO) and San Jose (SJC) airports for your trip from April 21-24, 2025? Both airports serve the SF Bay Area and might give you more options. Would you prefer to start with looking at flights or hotels first?\",\"last_message_topic\":\"flight_search_modification\",\"is_capable\":true}",
      "user all"
    ]
    current_date "November 1st 2025"
  }
  @@assert(is_capable, {{this.is_capable}})
}

test guardrail_test_20 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "assistant {\"agent_response\":\"Sure, I can include San Jose (SJC) in your search options. Would you like me to search for flights to both San Francisco (SFO) and San Jose (SJC) airports for your trip from April 21-24, 2025? Both airports serve the SF Bay Area and might give you more options. Would you prefer to start with looking at flights or hotels first?\",\"last_message_topic\":\"flight_search_modification\",\"is_capable\":true}",
      "user find me a flight to Puerto Rico"
    ]
    current_date "November 1st 2025"
  }
  @@check(not_supported, {{this.is_capable}})
}

test guardrail_test_21 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "assistant I'll start planning a new trip for you. Please tell me where you are going and your travel dates.",
      "user I need to travel from Seattle on May 19-23. 19-21 will be in Miami and then I’ll drive to Tampa and leave there on the 21. lets book the flights"
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{this.is_capable}})
}

test guardrail_test_fly_today {
  functions [CapabilitiesCheck]
  args {
    messages [
      "assistant The date you provided, April 2, 2025, is today. Flights cannot be booked for the same day. Please provide a future date for your trip.",
      "user I need to fly today"
    ]
    current_date "November 1st 2025"
  }
  @@assert(capable, {{this.is_capable}})
}

test guardrail_test_ask_calendar_capability {
  functions [CapabilitiesCheck]
  args {
    messages [
      "assistant I am unable to assist with integrating or using Zoom calendars, as this falls outside my travel-related capabilities. My expertise is focused on booking and managing travel plans. If you need help with travel arrangements, feel free to ask!",
      "user what calendar do you use?"
    ]
    current_date "November 1st 2025"
  }
  @@assert(capable, {{this.is_capable}})
}

test guardrail_test_ask_calendar_capability_2 {
  functions [CapabilitiesCheck]
  args {
    messages [
      "user can I use zoom calendar?",
    ]
    current_date "November 1st 2025"
  }
  @@assert(not_supported, {{not this.is_capable}})
}

test guardrail_test_ask_booking_2_beds {
  functions [CapabilitiesCheck]
  args {
    messages [
      "assistant {\"current_step\":\"HOTEL_BOOKING\",\"updated_hotel_search_core_criteria\":null,\"updated_hotel_search_additional_criteria\":null,\"updated_hotel_select_result\":{\"property_id\":5657933,\"room_product_id\":\"565793303_374711789_0_34_0\",\"hotel_name\":\"BLU-Tique, Akron, a Tribute Portfolio Hotel\",\"room_title\":\"Standard King Room\"},\"agent_response\":\"Got it! You’ve selected the BLU-Tique, Akron, a Tribute Portfolio Hotel for your stay from Thursday, April 10th to Friday, April 11th. The room is a Standard King Room with a mini-fridge and tea/coffee maker. I’ll proceed with booking this for you now. Let me know if you have any additional preferences or questions!\"}",
      "user I want 2 beds",
    ]
    current_date "November 1st 2025"
  }
  @@assert(capable, {{this.is_capable}})
}

test guardrail_test_ask_booking_cancel {
  functions [CapabilitiesCheck]
  args {
    messages [
      "assistant {\"accomodation_reservation_number\": \"1111111111\", \"accomodation_order_number\": \"1828769290\", \"accomodation_pincode_number\": \"8847\", \"hotel_selection\": \"Hyatt Regency San Francisco\", \"property_id\": null, \"room_product_id\": null, \"agent_response\": \"Your booking at the Hyatt Regency San Francisco has been successfully completed through our hotel partner. Here are the details of your booking:\\n- Order Number: 1828769290\\n- Reservation Number: 1111111111\\n- Hotel Name: Hyatt Regency San Francisco\\n- Room Title: Queen Room with Two Queen Beds and Accessible Shower\\n- Validated Price: $255.05\\n\\nI will add this booking to your calendar, and you will also receive a confirmation email from our partner Booking.com. If you have any further questions, please let me know.\", \"validated_price\": null, \"check_in_date\": \"2025-04-20\", \"check_out_date\": \"2025-04-21\", \"room_title\": \"\"}",
      "assistant Hi Yuchen, I\'ve noticed that you might have some new preferences for your travel arrangements. Would you like to update your preferences, especially regarding your preferred hotel brands?{\"message_type\": \"prompt\", \"suggested_preferences\": {\"options\": [{\"action\": \"My preferred hotel brands: Hyatt.\", \"title\": \"Preferred hotel brands\", \"detail\": \"Hyatt\", \"selected\": false}], \"submitted\": false}}",
      "assistant Now that your hotel has been booked, would you like to proceed with booking your flights?",
      "user Can you cancel the booking for me",
    ]
    current_date "November 1st 2025"
  }
  @@assert(capable, {{this.is_capable}})
}
