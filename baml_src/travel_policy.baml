class FlightPolicyComplianceResponse {
  policy_compliance bool[]? @description("whether each of the flights is within the company's travel policy")
  policy_compliance_reasons string[]? @description("a short reason (less than 10 words) why each flight is or isn't within the company's travel policy, referencing airline and flight number.")
}

class BamlFlightOption {
  airline_code string?
  flight_number string?
  departure_time string?
  arrival_time string?
  fare_option_name string?
  cabin string?
}

function DetermineFlightPolicyCompliance(flight_options: BamlFlightOption[]?, company_policy: string) -> FlightPolicyComplianceResponse {
  client GPT4o_AzureFirst
  prompt #"
    {{ _.role('system') }}

    You are a Travel Policy Checker. Your task is to evaluate flight options against the company's travel policy by analyzing the following criteria:

    - **Flight Option Name**
    - **Flight Cabin Class**
    - **Flight Duration**

    ### Evaluation Steps:

    1. **Default Policy Compliance**:
    - Check if the flight cabin class matches or below the company's default allowed class.

    2. **Exception Policy Compliance**:
    - If the flight cabin class does not match or below the default class, check if it qualifies under any exceptions.
    - Exceptions specify an allowed cabin class if the flight duration exceeds a certain number of hours. If there is job title or other criteria, evaluate accordingly.
    - Try your best knowledage to evaluate the flight option name since the input fare option name might be different from the policy. e.g. Delta Premium Select is same as PREMIUM_ECONOMY class, main cabin is ECONOMY class etc.
    - Clearly state if the flight meets the exception criteria based on above.
    - Note that cabin class names may vary by airline, so be sure to check the airline code and flight number for any specific exceptions. For example "Standard" of Air Canada is same as "Economy" of United Airlines or "Main Cabin" of Delta Airlines, or "Core" of JetBlue Airlines, or "World Traveller" of British Airways.

    ### Clearly State in Your Evaluation:

    - Whether each flight option complies with the default policy.
    - If not compliant with the default policy, explicitly evaluate compliance with any applicable exceptions.
    - Provide concise reasoning for each evaluation (less than 10 words per flight).

    ---
    Flight Options to Check:
    {{ flight_options }}
    ---
    company flight policy:
    {{ company_policy }}
    ---
    {{ _.role('system') }}
    {{ ctx.output_format }}
  "#
}
