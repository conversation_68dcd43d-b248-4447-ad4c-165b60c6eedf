
enum OttoCapabilityCategory {
  FLIGHT
  HOTEL
  COMPANY_TRAVEL_POLICY
  CALENDAR_INTEGRATION
  LOYALTY_PROGRAMS
  OTHERS @description(#"
  Other capabilities that do not fit into the above categories.
  "#)
}

class CapabilitySuggestion {
  agent_response string
  suggested_capability string? @description(#"One capability from the OttoCapabilities list that has not been mentioned in the chat history. Return null if all capabilities have been discussed."#)
  suggested_category OttoCapabilityCategory? @description(#"The category of the suggested capability."#)
}

function SuggestCapability(
  chat_history: string[],
  preferences: string,
  recent_suggested_capabilities: string[],
  connected_to_calendar: bool,
  self_intro: string?,
  current_action: string?,
  flight_search_count: int,
  hotel_search_count: int,
  flight_booked_count: int,
  hotel_booked_count: int,
  is_international_flight: bool,
  has_ffn: bool,
  has_company_travel_policy: bool
) -> CapabilitySuggestion {
  client GPT41
  prompt #"
  {{ ConversationHistory(chat_history, 0) }}

  {{ _.role("system")}}

  Role: Capability Analyzer
  {{ self_intro | default(GetSelfIntro()) }}

  Goal: Analyze the chat history to identify one capability that has not been mentioned or discussed.

  Procedure:
    - Review the entire conversation history to understand what capabilities have been mentioned or used
    - Compare against the available capabilities list provided
    - Consider the user's preferences and recent activity (timestamp) to prioritize which unused capability might be most relevant
    - Return exactly one capability that hasn't been discussed, or null if all have been covered

  ---
  Available Capabilities:
  {{OttoCapabilities()}}
  ---
  User Preferences:
  {{ preferences }}
  ---
  Already Suggested Capabilities:
  {{ recent_suggested_capabilities }}
  ---
  {% if connected_to_calendar %}
  User has connected to the calendar.
  {% else %}
  User has not connected to the calendar.
  {% endif %}
  ---
  User's current action: {{current_action}}
  ---
  {% if is_international_flight %}
  User is currently searching for an international flight.
  {% endif %}
  ---
  {% if flight_search_count == 0 %}
  User has not done any flight search.
  {% else %}
  User has done flight search {{ flight_search_count }} times.
  {% endif %}
  {% if hotel_search_count == 0 %}
  User has not done any hotel search.
  {% else %}
  User has done hotel search {{ hotel_search_count }} times.
  {% endif %}
  ---
  {% if flight_booked_count == 0 %}
  User has not booked any flight.
  {% else %}
  User has booked flight {{ flight_booked_count }} times.
  {% endif %}
  {% if hotel_booked_count == 0 %}
  User has not booked any hotel.
  {% else %}
  User has booked hotel {{ hotel_booked_count }} times.
  {% endif %}
  ---
  {% if has_ffn %}
  Yes, User has frequent flyer numbers on file.
  {% else %}
  No, User does not have frequent flyer numbers on file.
  {% endif %}
  ---
  {% if has_company_travel_policy %}
  Yes, User has a company travel policy on file.
  {% else %}
  No, User does not have a company travel policy on file.
  {% endif %}
  ---
  Guidelines:
    - Only suggest capabilities that are completely unmentioned in the chat history
    - Prioritize capabilities that align with user preferences when possible
    - Consider recency - capabilities discussed long ago may be worth re-suggesting
    - Return null if all capabilities have been adequately discussed
    - DO NOT suggest capabilities that have already suggested before.
    - If the user has already connected to the calendar, do not suggest calendar integration capabilities.
    - If the user already has frequent flyer numbers (FFN) on file, do not suggest loyalty program capabilities.
    - If the user has at least 1 preferences (home airport and preferred airline or preferred hotel brand) and did flight or hotel search, you can suggest connecting to the calendar. Making suggestion of connecting to the calendar more frequent, as it is more important feature.
    - If the user has done flight or hotel search, you can suggest company travel policy capabilities.
    - If the user already has a company travel policy on file, do not suggest company travel policy capabilities.
    - Please sugget company travel policy capabilities separately from loyalty programs, the category of company travel policy is COMPANY_TRAVEL_POLICY, but loyalty programs are in LOYALTY_PROGRAMS category.
    - Only suggest visa and entry requirment checks if the flight is international.
    - When suggesting visa and entry requirement checks, please ask the user to provide the destination country and citizenship.
    - No need to suggest flight search if user did flight search, instead you can suggest hotel related capabilities.
    - No need to suggest hotel search if user did hotel search, instead you can suggest flight related capabilities.
    - If the user has done flight search, but not booked any flight, you can suggest flight booking capabilities.
    - If the user has done hotel search, but not booked any hotel, you can suggest hotel booking capabilities.
    - 
    - Be creative in suggesting capabilities.
    - For agent response, provide a brief explanation of why the suggested capability is useful. e.g. Did you know you can book, cancel and change hotels just by asking me in the chat.
    - The response can be lesiurely and friendly, but should not be too long, limit to 15 ~ 20 words, otherwise user might not read it.

  {{ _.role("system")}}
  {{ ctx.output_format }}
  "#
}
