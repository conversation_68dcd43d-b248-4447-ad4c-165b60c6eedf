from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel

from server.database.models.user import User as UserORM
from server.database.models.user import UserRole


class User(BaseModel):
    id: int
    email: str
    preferred_name: str | None
    first_name: str
    last_name: str
    profile_picture: str
    created_date: datetime
    role: str = UserRole.user
    tutorial_completed: bool
    citizenship: list[str] | None
    organization_id: Optional[int] = None

    @property
    def name(self):
        if self.preferred_name:
            return self.preferred_name
        return self.first_name.capitalize() if self.first_name else ""

    class Config:
        from_attributes = True

    @classmethod
    def from_orm_user(cls, orm_user: UserORM, custom_fields: Dict[str, Any] = {}) -> "User":
        data = cls.model_validate(orm_user).model_dump()

        if custom_fields:
            data.update(custom_fields)
        return cls.model_validate(orm_user)

    def trim_name_fields(self) -> None:
        self.first_name = self.first_name.strip()
        self.last_name = self.last_name.strip()
