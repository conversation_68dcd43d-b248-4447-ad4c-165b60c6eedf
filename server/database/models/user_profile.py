from __future__ import annotations

from datetime import datetime
from typing import Optional

from sqlalchemy import Foreign<PERSON><PERSON>, delete, func, select
from sqlalchemy.orm import Mapped, mapped_column

from server.database.models.user import User as UserModel
from server.utils.pg_connector import Base, async_session


class LoginMethod:
    GOOGLE = "Google"
    MICROSOFT = "Microsoft"
    APPLE = "Apple"
    EMAIL = "Email"


class UserProfile(Base):
    __tablename__ = "google_profile"

    sub: Mapped[str] = mapped_column(primary_key=True)
    users_id: Mapped[int] = mapped_column(ForeignKey("users.id"))
    refresh_token: Mapped[str]
    resource_id: Mapped[Optional[str]] = mapped_column(nullable=True)
    created_date: Mapped[datetime] = mapped_column(server_default=func.current_timestamp(), nullable=False)

    calendar_enabled: Mapped[bool] = mapped_column(default=False, nullable=False)
    last_login_method: Mapped[str | None] = mapped_column(default=LoginMethod.GOOGLE, nullable=True)

    def __init__(
        self,
        sub: str,
        users_id: int,
        refresh_token: str,
        resource_id: str | None = None,
        last_login_method: str = LoginMethod.GOOGLE,
    ):
        self.sub = sub
        self.users_id = users_id
        self.refresh_token = refresh_token
        self.resource_id = resource_id
        self.last_login_method = last_login_method

    @staticmethod
    async def from_sub(sub: str):
        async with async_session() as session:
            async with session.begin():
                query = select(UserProfile).where(UserProfile.sub == sub)
                result = (await session.execute(query)).fetchone()

                if result is None:
                    return None

                return result[0]

    @staticmethod
    async def from_user_id(user_id: int) -> UserProfile | None:
        async with async_session() as session:
            async with session.begin():
                query = select(UserProfile).where(UserProfile.users_id == user_id)
                result = (await session.execute(query)).fetchone()

                if result is None:
                    return None

                return result[0]

    @staticmethod
    async def new_user(sub: str, refresh_token: str, resource_id: str | None, last_login_method: str, user: UserModel):
        async with async_session() as session:
            async with session.begin():
                session.add(user)
                await session.flush()

                user_profile = UserProfile(sub, user.id, refresh_token, resource_id, last_login_method)
                user_profile.users_id = user.id

                session.add(user_profile)
                return user_profile

    @staticmethod
    async def new_user_profile(user_profile: UserProfile):
        async with async_session() as session:
            async with session.begin():
                session.add(user_profile)
                return user_profile

    def get_token_payload(self, user: UserModel) -> dict[str, str]:
        return {
            "sub": str(user.id),
            "first_name": user.first_name,
            "last_name": user.last_name,
        }

    async def update_refresh_token(self, new_token: str, login_method: str):
        async with async_session() as session:
            async with session.begin():
                self.refresh_token = new_token
                self.last_login_method = login_method
                session.add(self)

    async def update_resource_id(self, resource_id: str):
        async with async_session() as session:
            async with session.begin():
                self.resource_id = resource_id
                session.add(self)

    async def enable_calendar(self, enable: bool):
        async with async_session() as session:
            async with session.begin():
                self.calendar_enabled = enable
                session.add(self)

    async def delete(self):
        async with async_session() as session:
            async with session.begin():
                query = delete(UserProfile).where(UserProfile.sub == self.sub)
                await session.execute(query)
