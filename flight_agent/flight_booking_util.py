import asyncio
from functools import partial
from typing import Any, Coroutine

from dateutil import parser

from flight_agent.flight_data import FlightInfo
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.user_profile import UserProfile
from server.schemas.authenticate.user import User
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.utils.booking_utils import extract_booking_dates_and_status
from server.utils.smtp import send_booking_email


class FlightBookingUtil:
    def __init__(
        self,
        thread: ChatThread,
        user_profile: UserProfile | None,
        user: User,
    ):
        self.thread = thread
        self.user_profile = user_profile
        self.user = user

    async def add_booking_to_calendar(self, new_booking: dict[str, Any]):
        if self.user_profile:
            # Update the Google Calendar Event associated with booking info
            google_calendar_api = CalendarProviderManager(user_profile=self.user_profile)

            if google_calendar_api.has_calendar_access():
                if new_booking["content"].get("legs", None) is not None:
                    legs = new_booking["content"].get("legs", [])
                    if legs:
                        FlightBookingUtil.create_legs_flight_calendar_event(
                            google_calendar_api,
                            legs[-1],
                            new_booking["content"].get("airline_confirmation_number") or "",
                            self.thread,
                        )

                elif new_booking["content"].get("outbound", None) is not None:
                    FlightBookingUtil.create_outbound_flight_calendar_event(
                        google_calendar_api,
                        new_booking["content"].get("outbound", {}),
                        new_booking["content"].get("airline_confirmation_number") or "",
                        self.thread,
                    )

                elif new_booking["content"].get("return", None) is not None:
                    FlightBookingUtil.create_return_flight_calendar_event(
                        google_calendar_api,
                        new_booking["content"].get("return", {}),
                        new_booking["content"].get("airline_confirmation_number") or "",
                        self.thread,
                    )

    async def save_booking_flights(
        self,
        new_booking: dict[str, Any],
        message_sender: partial[Coroutine[Any, Any, None]],
    ):
        start_date, end_date, status = extract_booking_dates_and_status("flight", new_booking.get("content", {}))

        if start_date:
            new_booking["start_date"] = start_date
        if end_date:
            new_booking["end_date"] = end_date
        if status:
            new_booking["status"] = status.name

        # Insert the new booking document
        new_booking["user_id"] = self.user.id
        new_booking_model: Booking = Booking(**new_booking)
        await Booking.new_booking(new_booking_model)

        if message_sender:
            await message_sender(message={"type": "itinerary_update"})

        new_booking_content = new_booking.get("content", {})

        await send_booking_email(
            "flight",
            self.user.email,
            self.thread.id,
            extra_info=f"spotnana_trip_id={new_booking_content.get('trip_id')}\n"
            f"airline_pnr={new_booking_content.get('airline_confirmation_number')}\n"
            f"spotnana_url={new_booking_content.get('manageBookingUrl')}",
        )

        # fire and forget
        asyncio.create_task(self.add_booking_to_calendar(new_booking))

    @staticmethod
    def map_flight_calendar_event(
        thread: ChatThread,
        flight: dict[str, Any],
        airline_confirmation_number: str,
    ):
        flight_info = FlightInfo.from_flight_data(flight)

        event_title = f"Flight {thread.title} ({', '.join(flight_info.flight_numbers)})"

        description: str = "\n".join(
            [
                f"{flight_info.airline} ({', '.join(flight_info.flight_numbers)})",
                f"{flight_info.origin_name} - {flight_info.destination_name}",
                f"Seat number: {flight_info.seat}",
                f"Cabin: {flight_info.cabin}",
                f"Confirmation number: {airline_confirmation_number}",
            ]
        )

        return (
            event_title,
            parser.isoparse(flight_info.departure_time),
            parser.isoparse(flight_info.arrival_time),
            description,
            flight_info.departure_timezone,
            flight_info.arrival_timezone,
            flight_info.location,
        )

    @staticmethod
    def create_flight_calendar_event(
        google_calendar_api: CalendarProviderManager,
        flight: dict[str, Any],
        airline_confirmation_number: str,
        thread: ChatThread,
    ):
        """
        Create a calendar event for a flight.

        :param google_calendar_api: Calendar provider manager instance
        :param flight: Flight data dictionary
        :param airline_confirmation_number: Airline confirmation number
        :param thread: Chat thread instance
        """
        (title, departure_date, arrival_date, description, departure_timezone, arrival_timezone, location) = (
            FlightBookingUtil.map_flight_calendar_event(thread, flight, airline_confirmation_number)
        )

        google_calendar_api.create_event(
            title,
            departure_date,
            arrival_date,
            description=description,
            timezone_start=departure_timezone,
            timezone_end=arrival_timezone,
            location=location,
        )

    # Convenience methods that delegate to the main implementation
    @staticmethod
    def create_legs_flight_calendar_event(
        google_calendar_api: CalendarProviderManager,
        legs_flight: dict[str, Any],
        airline_confirmation_number: str,
        thread: ChatThread,
    ):
        """Create a calendar event for a leg flight."""
        FlightBookingUtil.create_flight_calendar_event(
            google_calendar_api, legs_flight, airline_confirmation_number, thread
        )

    @staticmethod
    def create_outbound_flight_calendar_event(
        google_calendar_api: CalendarProviderManager,
        outbound_flight: dict[str, Any],
        airline_confirmation_number: str,
        thread: ChatThread,
    ):
        """Create a calendar event for an outbound flight."""
        FlightBookingUtil.create_flight_calendar_event(
            google_calendar_api, outbound_flight, airline_confirmation_number, thread
        )

    @staticmethod
    def create_return_flight_calendar_event(
        google_calendar_api: CalendarProviderManager,
        return_flight: dict[str, Any],
        airline_confirmation_number: str,
        thread: ChatThread,
    ):
        """Create a calendar event for a return flight."""
        FlightBookingUtil.create_flight_calendar_event(
            google_calendar_api, return_flight, airline_confirmation_number, thread
        )
