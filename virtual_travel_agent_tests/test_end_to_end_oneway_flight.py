import asyncio
import logging
from datetime import datetime
from enum import Enum
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

logger = logging.getLogger(__name__)


class FlightOneWaySteps(Enum):
    INIT_TRIP = "INIT_TRIP"
    SEARCH = "FLIGHT_SEARCH"
    VALIDATION = "FLIGHT_VALIDATION"
    BOOKING = "FLIGHT_BOOKING"

    def __str__(self):
        return self.value


def cheapest_flight_n_button(last_message, _):
    """
    Default implementation for selecting a flight.
    This selects the flight with the lowest price.
    """
    logger.info("[Callback] Selecting the cheapest flight via clicking the button.")
    flights = last_message["flights"]
    selected_flight = min(flights, key=lambda flight: float(flight["price"]["amount"]))
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": selected_flight["action"],
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


def earliest_flight_n_fuzzy(last_message, timed_template: str):
    """
    Pick the first flight and plug into the timed_template.
    """
    logger.info("[Callback] Selecting the first flight via text prompt.")
    flights = last_message["flights"]
    selected_flight = min(
        flights,
        key=lambda flight: flight["flight_segments"][0]["flight_stops"][0]["departure"],
    )
    departure_time_str = selected_flight["flight_segments"][0]["flight_stops"][0]["departure"]
    formatted_time = datetime.strptime(departure_time_str, "%Y-%m-%dT%H:%M:%S").strftime("%I:%M%p").lower().lstrip("0")
    selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"]
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": timed_template.format(departure_time=formatted_time),
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


def earliest_flight_n_vague(last_message, vague_prompt: str):
    """
    Default implementation for selecting a flight.
    This selects the flight with the lowest price.
    """
    logger.info("[Callback] Selecting the cheapest flight via clicking the button.")
    flights = last_message["flights"]
    selected_flight = min(
        flights,
        key=lambda flight: flight["flight_segments"][0]["flight_stops"][0]["departure"],
    )
    selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"]
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": vague_prompt,
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


def latest_flight_n_vague(last_message, vague_prompt: str):
    """
    Pick the last flight and plug into the timed_template.
    """
    logger.info("[Callback] Selecting the last flight via text prompt.")
    flights = last_message["flights"]
    selected_flight = max(
        flights,
        key=lambda flight: flight["flight_segments"][0]["flight_stops"][0]["departure"],
    )
    selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"]
    logger.info("[Callback] Selected flight: %s", selected_flight)
    return {
        "prompt": vague_prompt,
        "flight_number": selected_flight["flight_segments"][0]["flight_stops"][0]["flight_number"],
        "airline_code": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_code"],
        "airline_name": selected_flight["flight_segments"][0]["flight_stops"][0]["airline_name"],
        "selected_flight": selected_flight,
    }


# In 'The Terminator', the T-800 is sent back in time from 2029 to May 12, 1984, on a one-way mission to
# eliminate Sarah Connor. This precise date marks the beginning of the machine's relentless pursuit,
# setting in motion a chain of events that will determine the fate of humanity in the future war against Skynet.
#
default_start_date = datetime(2025, 4, 12)
default_flight_prompt = f"one-way to SFO UA on {default_start_date.strftime('%B %d, %Y')}"


# fmt: off
@patch("virtual_travel_agent.helpers.get_current_date_string", return_value="November 13th 2024")
@patch("sys.setprofile")
@patch("threading.setprofile")
@patch("server.utils.custom_profiler.CustomProfiler")
@pytest.mark.parametrize(
    "step_to_stop_test, start_trip_prompt, flight_selection_callback, flight_selection_callback_param, confirm_to_book_prompt",
    [
        # search for flight
        (FlightOneWaySteps.SEARCH, default_flight_prompt, "", "", ""),
        (FlightOneWaySteps.SEARCH, "SFO, one-way, UA, April 12.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "One-way, SFO, UA, April 12.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "One-way to SFO with United, 4/12/25.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Book SFO one-way on United for April 12.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "One-way to SFO with UA, April 12, 2025.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "One-way flight to San Francisco, UA, April 12.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "One-way to San Francisco with United, 12th April.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Book me a one-way flight to SFO with UA on April 12.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "One-way to San Francisco, United Airlines, April 12, 2025.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Please book me a one-way to SFO with United on 12 April 2025.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Book a one-way flight to San Francisco on United for April 12, ’25.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Arrange a one-way ticket to San Francisco with United for April 12, 2025.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Book me a one-way flight to San Francisco with United Airlines for 5/12/25.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Book me a flight to SFO, one-way, with United Airlines, for April 12th, 2025.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "I’d like to book a one-way flight to San Francisco, departing April 12, 2025, on United Airlines, please.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Please go ahead and book me a one-way flight to SFO, flying on United Airlines, departing on April 12, 2025.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "I need a one-way flight to San Francisco, California, flying with United Airlines on the 12th of April, 2025.", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Can you book a one-way trip to San Francisco International Airport, flying United Airlines on April 12, 2025?", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Could you arrange a one-way flight to San Francisco International Airport with United Airlines on April 12, 2025?", "", "", ""),
        (FlightOneWaySteps.SEARCH, "Could you secure a one-way ticket to San Francisco International, flying United Airlines, departing April 12, 2025?", "", "", ""),
        # outbound with exact time in message
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, cheapest_flight_n_button, "", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "select the one leave at {departure_time}", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "select the {departure_time} UA flight", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "lock in the {departure_time} United Airlines one for me", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "I'll take the United Airlines flight at {departure_time}, thanks!", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "The United Airlines flight at {departure_time} is the one for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "Book the United Airlines flight that leaves at {departure_time}.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "You know me, early bird style—{departure_time} UA it is!", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "I'll take the United Airlines flight at {departure_time}, feels like a good choice.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "Can you go ahead and book me on that {departure_time} UA flight? It looks like the best fit for my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "Let's finalize the UA option leaving at {departure_time}. It's perfect timing for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "I'd like you to secure the United Airlines flight that departs at {departure_time}. That one works perfectly for what I need.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "The United Airlines flight at {departure_time} aligns well with my plans. Let's go with that one.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "Let's settle on the {departure_time} UA flight. It's the best choice for this trip.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "After considering the schedules, I'd prefer to take the UA flight at {departure_time}. Please confirm it for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "Let's lock in the United Airlines flight that's scheduled for {departure_time}. It'll suit my travel plans perfectly.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "The UA flight departing at {departure_time} seems like the best match. Please go ahead and secure it for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "The UA option at {departure_time} stands out as the right choice. Can you take care of booking it for me?", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "I'm all set to confirm the United Airlines flight leaving at {departure_time}. Let's proceed with that one.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "I'd like you to secure the flight that departs at {departure_time} on UA. That one works perfectly for what I need.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "Could you please confirm the flight leaving at {departure_time} on United Airlines? I think that's the one I want to go with.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "Let's settle on the flight leaving at {departure_time} on UA. It's the best choice for this trip.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "I'd like to stick with the flight that departs at {departure_time} on United Airlines. Could you lock that one in for me?", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "That flight leaving at {departure_time} looks just right. Can you book that one for me, please?", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "Let's lock in the flight that's scheduled for {departure_time} on United Airlines. It'll suit my travel plans perfectly.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "I'm leaning towards the flight leaving at {departure_time} on UA. Let's lock that one in to finalize my plans.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "The option with the {departure_time} departure stands out as the right choice. Can you take care of booking it for me?", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_fuzzy, "I'm all set to confirm the flight leaving at {departure_time}. Let's proceed with that one", ""),
        # outbound with vague time description in message
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Book me earliest.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Earliest flight, please.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Get me on the first flight.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "First flight works; book it.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Book the first flight for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Earliest flight works, please book.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Book me on the earliest flight, please.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Please book the first available flight.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Can you secure the earliest flight for me?", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Please confirm the earliest flight for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Please arrange the earliest flight for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "The earliest flight looks good; book it for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "I’d like to take the earliest flight—can you book it?", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Would you mind booking me on the earliest available flight? It aligns best with my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Can you go ahead and book me on that earlist flight? It looks like the best fit for my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Would you please arrange my travel on the earliest flight option? It fits perfectly with my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Can you kindly take care of booking the earliest flight available? It appears to work best with my timing.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Can you go ahead and confirm a booking for me on the earliest flight available? It’s the best fit for my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Can you please make the necessary arrangements for me to be on the earliest flight? It’s the most convenient for my plans.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, earliest_flight_n_vague, "Could you go ahead and arrange for me to be booked on the earliest flight? It seems like it’s the most suitable option for my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Book me latest.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Latest flight, please.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Get me on the last flight.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Last flight works; book it.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Book the last flight for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Latest flight works, please book.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Book me on the latest flight, please.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Please book the last available flight.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Can you secure the latest flight for me?", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Please confirm the latest flight for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Please arrange the latest flight for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "The latest flight looks good; book it for me.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "I'd like to take the latest flight—can you book it?", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Would you mind booking me on the latest available flight? It aligns best with my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Can you go ahead and book me on that latest flight? It looks like the best fit for my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Would you please arrange my travel on the latest flight option? It fits perfectly with my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Can you kindly take care of booking the latest flight available? It appears to work best with my timing.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Can you go ahead and confirm a booking for me on the latest flight available? It’s the best fit for my schedule.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Can you please make the necessary arrangements for me to be on the latest flight? It’s the most convenient for my plans.", ""),
        (FlightOneWaySteps.VALIDATION, default_flight_prompt, latest_flight_n_vague, "Could you go ahead and arrange for me to be booked on the latest flight? It seems like it’s the most suitable option for my schedule.", ""),
        # fuzzy confirm terms
        (FlightOneWaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "confirm"),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Confirm my flights, thank you!"),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Okay, let's go ahead with this one."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Seems fine to me. You can confirm it."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Yes, please confirm the booking for me."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Go ahead and finalize my flight booking."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Yeah, that looks fine. Please handle it."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I think this should work. Please proceed."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Let's wrap this up. Please take care of it."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Alright, let's finalize things on your end."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "That'll do. You can take the next steps now."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "This should be good to go. Please lock it in."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Sure, this works for me. Let's move forward with it."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Lock in these flights and complete the booking process."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I'm okay with this. Go ahead with what needs to be done."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I approve this flight itinerary, proceed with the booking."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "This flight schedule aligns well with my travel plans. You can move forward with the reservation now. Thanks for handling this."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I approve this itinerary. Please finalize the reservation and ensure that the booking is confirmed as per the provided schedule."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Based on the details shared, this itinerary works perfectly for me. Kindly complete the booking process and confirm once it's done."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I've double-checked everything, and I'm satisfied with this itinerary. Please proceed to confirm the flights and secure the bookings."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I've reviewed the options and am ready to proceed. Could you please confirm these flights for me and send me the final booking details?"),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "The details of this itinerary meet my requirements perfectly. You have my go-ahead to proceed with the booking process and secure these flights."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "Please go ahead and finalize this booking for me. I trust everything looks good as per our discussion, and I appreciate your prompt action on this."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I am happy with the schedule and pricing of these flights. Kindly take the next steps necessary to lock in these reservations and complete the booking process."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "After thoroughly considering all the options provided, I have decided to move forward with this particular flight plan. Please confirm the booking at your earliest convenience."),
        # (FlightOnewaySteps.BOOKING, default_flight_prompt, cheapest_flight_n_button, "", "I have reviewed the itinerary details carefully and am satisfied with the selection. Please proceed to finalize the booking for these flights on my behalf. Thank you for your assistance."),
    ],
)
# fmt: on
@pytest.mark.asyncio
async def test_one_way_to_sfo_with_united_airline(
    mock_custom_profiler, mock_threading_setprofile, mock_sys_setprofile,
    _,
    step_to_stop_test,
    start_trip_prompt,
    flight_selection_callback,
    flight_selection_callback_param,
    confirm_to_book_prompt,
    mock_user,
    mock_thread,
    mock_payment_profile,
    mock_user_profile,
    mock_user_preference_json,
    mock_travel_context_json,
):
    mock_sys_setprofile.return_value = None
    mock_threading_setprofile.return_value = None
    mock_custom_profiler.return_value = MagicMock()
    mock_websocket = MagicMock()
    mock_websocket.send_json = AsyncMock()
    # fmt: off
    with patch("server.services.user.user_preferences.get_user_preferences", new_callable=AsyncMock, return_value=mock_user_preference_json), \
            patch("server.services.payment_profile.user_payment_profile.get_user_payment_profile", new_callable=AsyncMock, return_value=mock_payment_profile), \
            patch("server.services.trips.bookings.get_trip_travel_context", return_value=mock_travel_context_json), \
            patch("server.database.models.user_profile.UserProfile.from_user_id", new_callable=AsyncMock, return_value=mock_user_profile), \
            patch("server.services.trips.bookings.get_bookings", new_callable=AsyncMock, return_value={}), \
            patch("server.database.models.chat_thread.ChatThread.from_id", new_callable=AsyncMock, return_value=mock_thread), \
            patch("server.database.models.user_company_travel_policy.UserCompanyTravelPolicy.from_user_id", new_callable=AsyncMock, return_value=None), \
            patch("server.utils.mongo_connector.trip_travel_context_collection.update_one", new_callable=AsyncMock), \
            patch("virtual_travel_agent.virtual_travel_agent.VirtualTravelAgent.save_travel_context", new_callable=AsyncMock), \
            patch("virtual_travel_agent.langchain_chat_persistence.PostgresChatMessageHistory.apersist", new_callable=AsyncMock), \
            patch("server.utils.smtp.send_booking_email", new_callable=AsyncMock), \
            patch("server.services.calendar_api.calendar_provider.CalendarProviderManager.has_calendar_access", return_value=False), \
            patch("server.database.models.checkpoint.Checkpoint.from_thread_id", new_callable=AsyncMock, return_value=[]):
        # fmt: on
        from server.api.v1.endpoints.websocket import WSConnectionManager

        ws_manager = WSConnectionManager(user=mock_user)

        try:
            # Step 0. Initiate the trip
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={"type": "trip_init", "tripId": mock_thread.id},
            )

            await asyncio.sleep(10)

            # just expect the first assistant message
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list]
            for m in messages:
                logger.debug("[TripInit] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            for m in messages:
                if m["status"] != "success":
                    raise Exception(f"Error in message: {m}")            

            # Step 1. Send flight prompt
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": start_trip_prompt,
                },
            )

            await asyncio.sleep(60)

            # at this point flight search for the outbound is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            for m in messages[processed_message_count:]:
                logger.debug("[FlightSearch] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            for m in messages:
                if m["status"] != "success":
                    raise Exception(f"Error in message: {m}")            
            with_travel_context = [m for m in messages[-5:] if m.get("travel_context")]
            assert (
                any("United" in airline for airline in with_travel_context[-1]["travel_context"]["flight_search_additional_criteria"]["preferred_airline_codes"])
                or "UA" in with_travel_context[-1]["travel_context"]["flight_search_additional_criteria"]["preferred_airline_codes"]
            )
            with_flights = [m for m in messages[-5:] if m.get("flights")]
            assert all(float(flight["price"]["amount"]) > 0 for flight in with_flights[-1]["flights"])
            assert all(
                "UA" == flight["flight_segments"][0]["flight_stops"][0]["airline_code"]
                for flight in with_flights[-1]["flights"]
            )
            assert all(flight["price"]["currency"] == "USD" for flight in with_flights[-1]["flights"])
            assert all(flight["flight_segments"][0]["origin_code"] == "SEA" for flight in with_flights[-1]["flights"])
            assert all(flight["flight_segments"][-1]["destination_code"] == "SFO" for flight in with_flights[-1]["flights"])
            logger.info(
                "[FlightSearch] Search result: %d flights found",
                len(with_flights[-1]["flights"]),
            )
            if step_to_stop_test == FlightOneWaySteps.SEARCH:
                logger.info("[FlightSearch] Stopping test at step: %s", step_to_stop_test)
                return

            # Step 2. pick the outbound flight with a prompt (click or text)
            flight_selection = flight_selection_callback(with_flights[-1], flight_selection_callback_param)
            pick_flight_prompt = flight_selection["prompt"]
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": pick_flight_prompt,
                },
            )

            await asyncio.sleep(40)

            # at this point flight validation is done
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            last_prompt_type_message = [m for m in messages[-5:] if m["type"] == "prompt"]
            for m in messages:
                logger.debug("[FlightValidation] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            assert all(m["status"] == "success" for m in messages)
            
            # flight details like flight_number, airline_code should be listed in the response.
            assert flight_selection["flight_number"] in last_prompt_type_message[-1]["text"]
            assert (
                flight_selection["airline_code"] in last_prompt_type_message[-1]["text"]
                or flight_selection["airline_name"] in last_prompt_type_message[-1]["text"]
            )
            logger.info("[FlightValidation] Validation result: %s", last_prompt_type_message[-1]["text"])
            if step_to_stop_test == FlightOneWaySteps.VALIDATION:
                logger.info("[FlightValidation] Stopping test at step: %s", step_to_stop_test)
                return

            # Step 3. confirm the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": confirm_to_book_prompt,
                },
            )

            await asyncio.sleep(60)

            # at this point flight should have been booked
            assert mock_websocket.send_json.call_count > 0
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            for m in messages[processed_message_count:]:
                logger.debug("[FlightBooking] message: %s", m)
            processed_message_count = mock_websocket.send_json.call_count
            for m in messages:
                if m["status"] != "success":
                    raise Exception(f"Error in message: {m}")
            last_prompt_type_message = [m for m in messages[-5:] if m["type"] == "prompt"]
            logger.info("[FlightBooking] Booking result: %s", last_prompt_type_message[-2]["text"])
            assert any("confirmation ID" in m["text"] for m in last_prompt_type_message)
            assert any("airline" in m["text"] for m in messages[-3:])
            assert any(m["type"] == "itinerary_update" for m in messages[-3:])
            logger.info("[FlightBooking] last message: %s", last_prompt_type_message[-2]["text"])
            if step_to_stop_test == FlightOneWaySteps.BOOKING:
                logger.info("[FlightBooking] Stopping test at step: %s", step_to_stop_test)
            
            # Step 4. confirm cancel the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": "I want to cancel flight",
                },
            )

            await asyncio.sleep(40)
            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            last_prompt_type_message = [m for m in messages[-5:] if m["type"] == "prompt"]
            logger.info("[FlightCancel] Confirm Cancel result: %s", last_prompt_type_message[-1]["text"])

            # Step 5. go ahead cancel the flight
            await ws_manager.on_receive(
                websocket=mock_websocket,
                message={
                    "type": "prompt",
                    "tripId": mock_thread.id,
                    "text": "go ahead",
                },
            )

            await asyncio.sleep(40)

            messages = [m[0][0] for m in mock_websocket.send_json.call_args_list if not m[0][0].get("partial")]
            logger.info("[FlightCancel] Go ahead Cancel result: %s", messages[-3:])

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            logger.error(e)
            raise
