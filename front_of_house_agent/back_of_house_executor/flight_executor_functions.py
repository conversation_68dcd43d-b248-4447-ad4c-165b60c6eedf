import asyncio
import json
from typing import Any, Callable, Coroutine, Dict, Optional, Tuple

from langchain_core.messages import AIMessage, BaseMessage, FunctionMessage

import front_of_house_agent.back_of_house_executor.flight_and_hotel_executor as fhe
from baml_client import b
from baml_client.tracing import trace
from baml_client.types import (
    FlightCreditsResponse,
    FlightPlanningStep,
    FlightPolicy,
    FlightSearchAdditionalCriteria,
    FlightSearchCoreCriteria,
    FlightType,
    SeatMatchingResult,
    SeatSelectionForFlight,
)
from equivalent_flight.flight_comparator import FlightComparator
from flight_agent.flights_tools import FlightSearchTools, airline_loyalty_program_mapping
from front_of_house_agent.adapter import map_websocket_message
from front_of_house_agent.common_models import (
    EnrichedFlightSelectResult,
    FlightCheckoutMissField,
    FlightOption,
    FlightSearchParams,
    FlightSearchSource,
    FlightSearchType,
    SpotnanaFlightOption,
    SpotnanaMatchedFlight,
)
from front_of_house_agent.flight_utils import (
    get_flight_search_id_for_return_search_from_serp_flight_id,
    track_fallback_spotnana_metrics,
    track_serp_not_found_metrics,
)
from front_of_house_agent.serp_common_models import Booking
from front_of_house_agent.spotnana_client import SpotnanaClient, SpotnanaFlightSearchParam
from llm_utils.llm_utils import is_valid_future_date
from server.database.models.user import User as UserDB
from server.database.models.user import UserRole
from server.database.models.user_company_travel_policy import UserCompanyTravelPolicy
from server.schemas.authenticate.user import User
from server.schemas.spotnana.create_spotnana_profile import CreateSpotnanaProfile
from server.schemas.spotnana.flight_statuses import FlightStatuses
from server.services.memory.trips.memory_modules.bookings_memory import BookingOperation, BookingsMemory
from server.services.payment_profile.spotnana_profile import update_spotnana_user_profile
from server.services.trips.flight_card import construct_flight_itinerary_dict
from server.services.trips.flight_credits_api import flight_credits_api
from server.services.user_profile.loyalty_programs import get_user_profile_flights_loyalty_programs
from server.services.user_profile.payment_information import (
    get_missing_user_profile_payment_information_fields,
    get_user_profile_payment_information,
)
from server.services.user_profile.personal_information import (
    get_missing_user_profile_personal_information_fields,
    get_user_profile_personal_information,
)
from server.utils.logger import logger
from server.utils.settings import AgentTypes, settings
from virtual_travel_agent.common_models import AgentError
from virtual_travel_agent.helpers import console_masks, get_current_date_string
from virtual_travel_agent.timings import Timings

flight_mask = console_masks["flight"]


async def save_flight_booking(
    fh_executor: "fhe.TripPlanExecutor",
    trip_id: str,
    confirmation_id: str,
    airline_confirmation_number: str,
    selected_flights: list[dict[str, Any]],
):
    logger.info(f"Saving flight booking with trip_id: {trip_id}", flight_mask)
    if settings.OTTO_ENV.upper() in ["DEV"]:
        spotnana_url = f"https://sboxmeta-app.partners.spotnana.com/trips/{trip_id}"
    else:
        spotnana_url = f"https://app.spotnana.com/trips/{trip_id}"

    # Create a new document for this booking
    new_booking: dict[str, Any] = {
        "thread_id": fh_executor.thread.id,
        "user_id": fh_executor.user.id,
        "type": "flight",
        "content": construct_flight_itinerary_dict(selected_flights, confirmation_id),
    }
    new_booking["content"]["trip_id"] = trip_id
    new_booking["content"]["airline_confirmation_number"] = airline_confirmation_number
    new_booking["content"]["status"] = FlightStatuses.PENDING.value
    new_booking["content"]["manageBookingUrl"] = spotnana_url

    await fh_executor.flight_booking_util.save_booking_flights(new_booking, fh_executor.message_sender)
    return new_booking


async def flight_matcher_spotnana(
    fh_executor: "fhe.TripPlanExecutor",
    outbound_flight: FlightOption,
    return_flight: Optional[FlightOption],
    selected_flight_for_segment: Optional[Dict[str, FlightOption]],
) -> Optional[SpotnanaMatchedFlight]:
    user_in_company = await FlightSearchTools.query_user_spotnana(fh_executor.user.email)
    user_guid_in_spotnana: str | None = (user_in_company.get("elements") or [{}])[0].get("id")

    if selected_flight_for_segment:
        leg_len = len(selected_flight_for_segment)
        candidate_flight_options: list[Optional[FlightOption]] = [None] * leg_len
        for key, flight_option in selected_flight_for_segment.items():
            if flight_option:
                candidate_flight_options[int(key)] = flight_option
        search_id, flight_id = None, None
        matched_flight_options = []
        for index, flight_option in enumerate(candidate_flight_options):
            assert flight_option is not None, "flight option should not be None here in flight_matcher_spotnana"
            spotnana_options, search_id = await SpotnanaClient.search_flights_spotnana_legs(
                candidate_flight_options=[option for option in candidate_flight_options if option is not None],
                segment_index=index,
                previous_selected_flight_id=flight_id,
                previous_search_id=search_id,
            )

            matched_flight_option = await FlightComparator.get_exact_match_flight(
                flight_option, spotnana_options, True if index < len(candidate_flight_options) - 1 else False
            )

            if not matched_flight_option:
                return None
            flight_id = matched_flight_option.spotnana_flight_id
            matched_flight_options.append(matched_flight_option)
        return SpotnanaMatchedFlight(
            spotnana_outbound_flight=matched_flight_options[0],
            spotnana_return_flight=matched_flight_options[-1],
            spotnana_search_id=search_id or "",
        )

    param = SpotnanaFlightSearchParam(
        is_outbound_search=True,
        search_id=None,
        selected_outbound_flight_id=None,
        departure_airport_code=outbound_flight.stops[0].departure_airport_code,
        arrival_airport_code=outbound_flight.stops[-1].arrival_airport_code,
        outbound_date=outbound_flight.stops[0].departure_time.split(" ")[0],
        return_date=return_flight.stops[0].departure_time.split(" ")[0] if return_flight else None,
        flight_number=outbound_flight.stops[0].flight_number,
        flight_airline_code=outbound_flight.stops[0].airline_code,
        start_time=outbound_flight.stops[0].departure_time,
        is_one_way=return_flight is None,
        spotnana_user_id=user_guid_in_spotnana or "",
    )
    outbound_flight_options, search_id = await SpotnanaClient.search_flights_spotnana(param)
    outbound_flight_choice = await FlightComparator.get_exact_match_flight(
        outbound_flight, outbound_flight_options, True if return_flight is None else False
    )

    if not outbound_flight_choice:
        return None

    if return_flight is None:
        return SpotnanaMatchedFlight(
            spotnana_outbound_flight=outbound_flight_choice,
            spotnana_search_id=search_id,
        )

    param = SpotnanaFlightSearchParam(
        is_outbound_search=False,
        search_id=search_id,
        selected_outbound_flight_id=outbound_flight_choice.spotnana_flight_id,
        departure_airport_code=outbound_flight.stops[0].departure_airport_code,
        arrival_airport_code=outbound_flight.stops[-1].arrival_airport_code,
        outbound_date=outbound_flight.stops[0].departure_time.split(" ")[0],
        return_date=return_flight.stops[0].departure_time.split(" ")[0] if return_flight else None,
        flight_number=return_flight.stops[0].flight_number,
        flight_airline_code=return_flight.stops[0].airline_code,
        start_time=return_flight.departure_time,
        is_one_way=False,
        spotnana_user_id=user_guid_in_spotnana or "",
    )
    return_flight_options, search_id = await SpotnanaClient.search_flights_spotnana(param)

    return_flight_choice = await FlightComparator.get_exact_match_flight(return_flight, return_flight_options, True)
    if not return_flight_choice:
        return None

    return SpotnanaMatchedFlight(
        spotnana_outbound_flight=outbound_flight_choice,
        spotnana_return_flight=return_flight_choice,
        spotnana_search_id=search_id,
    )


@trace
async def flight_validation(
    fh_executor: "fhe.TripPlanExecutor",
    outbound_flight: FlightOption,
    return_flight: Optional[FlightOption],
    selected_flight_for_segment: Optional[Dict[str, FlightOption]],
    flight_selected_result: EnrichedFlightSelectResult | None,
    seat_selection_for_flight: list[SeatSelectionForFlight],
    flight_search_source: FlightSearchSource | None,
    spotnana_search_id: str | None,
    spotnana_flight_id: str | None,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]] | None,
):
    spotnana_matched_flight = None
    spotnana_outbound_flight = None
    spotnana_return_flight = None

    fh_executor.schedule_send_message(
        message={
            "type": "search_update",
            "text": "I'm collecting all the details for you now — hold tight...",
            "isBotMessage": True,
            "expectResponse": False,
        }
    )

    if flight_search_source == FlightSearchSource.FALLBACK_SPOTNANA:
        assert spotnana_search_id is not None, "spotnana_search_id is required for fallback search"
        assert spotnana_flight_id is not None, "spotnana_flight_id is required for fallback search"
        data = await FlightSearchTools.get_selected_itinerary_spotnana(spotnana_search_id, spotnana_flight_id)
    else:
        assert flight_selected_result is not None, "flight_selected_result is required for flight validation"
        assert flight_selected_result.search_id is not None, "spotnana_search_id is required for flight validation"
        assert flight_selected_result.matched_flight_id is not None, (
            "spotnana_flight_id is required for flight validation"
        )
        data = await FlightSearchTools.get_selected_itinerary_spotnana(
            flight_selected_result.search_id, flight_selected_result.matched_flight_id
        )

    if data.get("itinerary"):
        flight_option_dicts = FlightSearchTools.flattenFlights_itin(data.get("itinerary"))
        for flight_option_dict in flight_option_dicts:
            if not spotnana_outbound_flight:
                spotnana_outbound_flight = SpotnanaFlightOption.from_spotnan_dict(flight_option_dict)
            elif not spotnana_return_flight:
                spotnana_return_flight = SpotnanaFlightOption.from_spotnan_dict(flight_option_dict)

    seat_selection_summary = []

    seat_price = 0
    flights = FlightSearchTools.map_flights_in_itin(data.get("itinerary", {}))
    for seat_info in seat_selection_for_flight:
        seat = seat_info.seat_number
        price = seat_info.price
        airline_code = None
        flight_number = None
        for i, flight in enumerate(flights):
            if (
                flight.get("origin", {}).get("airportCode") == seat_info.start_airport
                and flight.get("destination", {}).get("airportCode") == seat_info.end_airport
            ):
                airline_code = flight.get("airline_code")
                flight_number = flight.get("flight_number")
                break

        seat_selection_summary.append(
            {"seat": seat, "price": price or 0, "airline_code": airline_code, "flight_number": flight_number}
        )
        seat_price += float(price or 0)
    is_employee = bool(fh_executor.user.organization_id)
    if selected_flight_for_segment:
        all_segment_dict = {}
        last_flight_option = None
        for index, flight_option in selected_flight_for_segment.items():
            all_segment_dict[index] = flight_option.model_dump(
                exclude_none=True, exclude_unset=True, exclude={"total_price"}
            )
            if int(index) == len(selected_flight_for_segment) - 1:
                last_flight_option = flight_option
        result = await b.TripPlanValidationSummaryForMultileg(
            current_date=get_current_date_string(fh_executor.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            seat_selections=json.dumps(seat_selection_summary),
            all_flight_selection=json.dumps(all_segment_dict),
            seat_price=seat_price,
            total_price=last_flight_option.total_price if last_flight_option else None,
            is_employee=is_employee,
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()
    else:
        assert spotnana_outbound_flight, "spotnana outbound flight is required"
        result = await b.TripPlanValidationSummary(
            current_date=get_current_date_string(fh_executor.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            seat_selections=json.dumps(seat_selection_summary),
            selected_outbound_flight=outbound_flight.model_dump_json(
                exclude={"total_price", "total_duration", "total_distance_miles"}
            ),
            matched_outbound_flight=spotnana_outbound_flight.model_dump_json(
                exclude={"total_price", "total_duration", "total_distance_miles"}
            ),
            selected_return_flight=return_flight.model_dump_json(
                exclude={"total_price", "total_duration", "total_distance_miles"}
            )
            if return_flight
            else None,
            matched_return_flight=spotnana_return_flight.model_dump_json(
                exclude={"total_price", "total_duration", "total_distance_miles"}
            )
            if spotnana_return_flight
            else None,
            original_total_price=return_flight.total_price if return_flight else outbound_flight.total_price,
            matched_total_price=spotnana_return_flight.total_price
            if spotnana_return_flight
            else spotnana_outbound_flight.total_price,
            seat_price=seat_price,
            is_employee=is_employee,
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()
    response = result

    # Check if user has valid payment profile
    new_message = await check_payment_profile_message(fh_executor.user.id)
    if new_message:
        if message_persistor:
            # Here, message_persistor is None when this function is not expected to
            # persist (and send) message that it's returnning.
            await message_persistor([new_message])
            message = await map_websocket_message(
                new_message,
                None,
                None,
            )
            await fh_executor.message_sender(message={**message[0]})
        # ... except the open form message
        await fh_executor.message_sender(
            message={
                "type": "open_flights_payment_form",
            }
        )
        return new_message

    function_message = FunctionMessage(
        content="",
        name="FlightValiadtionSummary",
        additional_kwargs={
            "function_call": {
                "name": "FlightValiadtionSummary",
                "arguments": json.dumps(
                    {
                        "agent_response": response,
                        "final_outbound_flight_option": spotnana_matched_flight.spotnana_outbound_flight.model_dump(
                            exclude_none=True
                        )
                        if spotnana_matched_flight and spotnana_matched_flight.spotnana_outbound_flight
                        else None,
                        "final_return_flight_option": spotnana_matched_flight.spotnana_return_flight.model_dump(
                            exclude_none=True
                        )
                        if spotnana_matched_flight and spotnana_matched_flight.spotnana_return_flight
                        else None,
                    }
                ),
            },
            "agent_classification": AgentTypes.FLIGHTS,
            "step": FlightPlanningStep.FLIGHT_VALIDATION.value,
        },
    )
    if message_persistor:
        await message_persistor([function_message])

        message = await map_websocket_message(
            function_message,
            None,
            None,
        )
        await fh_executor.message_sender(message={**message[0]})
    return function_message


async def flight_checkout_with_match(
    fh_executor: "fhe.TripPlanExecutor",
    outbound_flight: FlightOption,
    return_flight: Optional[FlightOption],
    selected_flight_for_segment: Optional[Dict[str, FlightOption]],
    flight_selected_result: EnrichedFlightSelectResult | None,
    preferred_seat_types: list[str],
    flight_search_source: FlightSearchSource | None,
    spotnana_search_id: str | None,
    spotnana_flight_id: str | None,
):
    spotnana_matched_flight = None
    air_option_id = None
    spotnana_outbound_flight = None
    spotnana_return_flight = None
    if flight_search_source == FlightSearchSource.FALLBACK_SPOTNANA:
        assert spotnana_search_id is not None, "spotnana_search_id is required for fallback search"
        assert spotnana_flight_id is not None, "spotnana_flight_id is required for fallback search"
        data = await FlightSearchTools.get_selected_itinerary_spotnana(spotnana_search_id, spotnana_flight_id)

        if data.get("itinerary"):
            flight_option_dicts = FlightSearchTools.flattenFlights_itin(data.get("itinerary"))
            for flight_option_dict in flight_option_dicts:
                if not spotnana_outbound_flight:
                    spotnana_outbound_flight = SpotnanaFlightOption.from_spotnan_dict(flight_option_dict)
                elif not spotnana_return_flight:
                    spotnana_return_flight = SpotnanaFlightOption.from_spotnan_dict(flight_option_dict)

    else:
        spotnana_matched_flight = await flight_matcher_spotnana(
            fh_executor, outbound_flight, return_flight, selected_flight_for_segment
        )
        seat_selection_to_store = None
        fare_basis_codes = None
        if not spotnana_matched_flight:
            response = "The selected flights is expired or not available. Please search again."
            return (
                None,
                FunctionMessage(
                    content=response,
                    name="TripPlanV2ValidationSummaryResponse",
                    additional_kwargs={
                        "function_call": {
                            "arguments": json.dumps({"agent_response": response}),
                        },
                        "agent_classification": AgentTypes.FLIGHTS,
                    },
                ),
                None,
                None,
                {},
            )
        else:
            fh_executor.schedule_send_message(
                message={
                    "type": "search_update",
                    "text": "I'm checking seats for you now — hold tight while I confirm all the details for you...",
                    "isBotMessage": True,
                    "expectResponse": False,
                }
            )

            air_option_id = (
                spotnana_matched_flight.spotnana_return_flight.spotnana_flight_id
                if spotnana_matched_flight.spotnana_return_flight
                else spotnana_matched_flight.spotnana_outbound_flight.spotnana_flight_id
            )
    flight_policy = None

    company_admin = (
        await UserDB.from_organization_id_and_role(fh_executor.user.organization_id, UserRole.company_admin)
        if fh_executor.user.organization_id
        else None
    )
    policy_user_id = company_admin.id if company_admin else fh_executor.user.id
    company_policy = await UserCompanyTravelPolicy.from_user_id(policy_user_id)

    has_company_policy = (
        company_policy is not None
        and company_policy.parsed_travel_policy is not None
        and company_policy.parsed_travel_policy.get("flight_policy") is not None
    )
    if has_company_policy:
        assert company_policy is not None and company_policy.parsed_travel_policy is not None
        parsed_flight_policy = company_policy.parsed_travel_policy.get("flight_policy")
        assert parsed_flight_policy is not None
        flight_policy = FlightPolicy(**parsed_flight_policy)

    company_allow_paid_seat = flight_policy.allow_paid_seat if flight_policy else None

    results = await asyncio.gather(
        seat_selection(
            user=fh_executor.user,
            spotnana_search_id=spotnana_matched_flight.spotnana_search_id
            if spotnana_matched_flight
            else spotnana_search_id,
            spotnana_itinerary_id=(
                spotnana_matched_flight.spotnana_return_flight.spotnana_flight_id
                if spotnana_matched_flight and spotnana_matched_flight.spotnana_return_flight
                else None
            )
            or (spotnana_flight_id if return_flight else None)
            or (
                spotnana_matched_flight.spotnana_outbound_flight.spotnana_flight_id
                if spotnana_matched_flight and spotnana_matched_flight.spotnana_outbound_flight
                else None
            )
            or (spotnana_flight_id if return_flight is None else None),
            preferred_seat_types=preferred_seat_types,
            company_allow_paid_seat=company_allow_paid_seat,
        ),
        SpotnanaClient.get_fare_basis_codes(
            (spotnana_matched_flight.spotnana_search_id if spotnana_matched_flight else spotnana_search_id) or "",
            air_option_id or spotnana_flight_id or "",
        ),
    )
    agent_response, _, seat_selection_to_store = results[0]

    fare_basis_codes = results[1]

    final_spotnana_search_id = (
        spotnana_matched_flight.spotnana_search_id if spotnana_matched_flight else spotnana_search_id
    )

    final_spotnana_flight_id = (
        spotnana_flight_id
        if spotnana_matched_flight is None
        else spotnana_matched_flight.spotnana_outbound_flight.spotnana_flight_id
        if spotnana_matched_flight.spotnana_return_flight is None
        else spotnana_matched_flight.spotnana_return_flight.spotnana_flight_id
    )

    proceed_to_validation_message = ""

    if not seat_selection_to_store:
        proceed_to_validation_message = "I see this flight does not allow seat selection, so I'll just proceed to next step to validate your flight selection."

    elif all(not x.price for seats in seat_selection_to_store.values() for x in seats):
        proceed_to_validation_message = "I see there is no paid seat available for your selected flights, so I'll just reserve the best free available seats for you and proceed to next step to validate your flight selection."
    elif "any seat" in list(map(lambda x: x.lower(), preferred_seat_types)):
        proceed_to_validation_message = "I see you didn't have a specific seat type, so I'll just reserve the best available seats for you and proceed to next step to validate your flight selection."
    auto_proceed_msg = None
    if proceed_to_validation_message:
        fh_executor.schedule_send_message(
            message={
                "type": "search_update",
                "text": proceed_to_validation_message,
                "isBotMessage": True,
                "expectResponse": False,
            }
        )
        auto_proceed_msg = AIMessage(
            content=proceed_to_validation_message,
            additional_kwargs={
                "agent_classification": AgentTypes.FLIGHTS,
                "step": FlightPlanningStep.FLIGHT_CHECKOUT.value,
            },
        )

        if flight_selected_result:
            flight_selected_result.search_id = final_spotnana_search_id
            flight_selected_result.matched_flight_id = final_spotnana_flight_id

        function_message = await flight_validation(
            fh_executor=fh_executor,
            outbound_flight=outbound_flight,
            return_flight=return_flight,
            selected_flight_for_segment=selected_flight_for_segment,
            flight_selected_result=flight_selected_result,
            seat_selection_for_flight=[
                item for seats in seat_selection_to_store.values() for item in seats if not item.price
            ],
            flight_search_source=flight_search_source,
            spotnana_search_id=spotnana_search_id,
            spotnana_flight_id=spotnana_flight_id,
            message_persistor=None,
        )
        function_message.additional_kwargs.setdefault("function_call", {})["fare_basis_codes"] = fare_basis_codes

    else:
        function_message = FunctionMessage(
            content="",
            name="FlightCheckoutSeatSummary",
            additional_kwargs={
                "function_call": {
                    "name": "FlightCheckoutSeatSummary",
                    "arguments": json.dumps(
                        {
                            "agent_response": agent_response,
                            "final_outbound_flight_option": spotnana_matched_flight.spotnana_outbound_flight.model_dump(
                                exclude_none=True
                            )
                            if spotnana_matched_flight and spotnana_matched_flight.spotnana_outbound_flight
                            else None,
                            "final_return_flight_option": spotnana_matched_flight.spotnana_return_flight.model_dump(
                                exclude_none=True
                            )
                            if spotnana_matched_flight and spotnana_matched_flight.spotnana_return_flight
                            else None,
                            "fare_basis_codes": fare_basis_codes,
                        }
                    ),
                },
                "agent_classification": AgentTypes.FLIGHTS,
                "step": FlightPlanningStep.FLIGHT_CHECKOUT.value,
            },
        )

    return (
        auto_proceed_msg,
        function_message,
        final_spotnana_search_id,
        final_spotnana_flight_id,
        {k: v for k, seats in seat_selection_to_store.items() for v in seats if not v.price},
    )


async def checking_requirment_before_validation(
    fh_executor: "fhe.TripPlanExecutor", preferred_seat_types: list[str], selected_airline_codes: list[str]
):
    miss_info: dict[str, str] = {}
    if not preferred_seat_types:
        miss_info[FlightCheckoutMissField.PREFFERED_SEAT_TYPE.value] = "preferred seat type is missing"
    if not fh_executor.thread.frequent_flyer_attempted_flight:
        # Get selected airline codes for FFN validation

        # Get stored FFNs from user profile
        user_profile_flights_loyalty_programs: dict[str, Any] | None = await get_user_profile_flights_loyalty_programs(
            fh_executor.user.id
        )
        stored_freq_flyer_ids = {}
        if user_profile_flights_loyalty_programs is not None:
            stored_freq_flyer_ids = (
                {
                    program["IATACode"]: program["number"]
                    for program in user_profile_flights_loyalty_programs.get("loyaltyPrograms", [])
                }
                if user_profile_flights_loyalty_programs.get("loyaltyPrograms", []) is not None
                else {}
            )

        # Check for missing FFNs
        missing_ffn_airlines = FlightSearchTools.only_airlines_missing_freq_flyer_id(
            selected_airline_codes, stored_freq_flyer_ids
        )
        loyalty_programs = set(
            [
                airline_loyalty_program_mapping[code]
                for code in missing_ffn_airlines
                if code in airline_loyalty_program_mapping
            ]
        )

        if missing_ffn_airlines and not fh_executor.thread.frequent_flyer_attempted_flight:
            await fh_executor.thread.update_fields({"frequent_flyer_attempted_flight": True})
            miss_info[FlightCheckoutMissField.FFN.value] = (
                f"We don't have a {' or '.join(loyalty_programs)} number for you on file. Can you please tell me your {' or '.join(loyalty_programs)} number or a partner airline Frequent Flyer number? So that I can pick the best seat for you (in case you qualify for special seats) and so that you can collect miles and status."
                f"If you don't have one, then just let me know."
            )
    if not miss_info:
        return None
    t = Timings("BAML: RephraseRequestForMissingOrInvalidInfo")
    combined_response = await b.RephraseRequestForMissingOrInvalidInfo(
        sentences=[
            "Looks like there're something to clarify about your flight before I could proceed.",
            *miss_info.values(),
        ],
        current_date=get_current_date_string(fh_executor.timezone),
        user_name=fh_executor.user.name,
        self_intro=settings.OTTO_SELF_INTRO,
        convo_style=settings.OTTO_CONVO_STYLE,
        need_response_with_name=False,
        baml_options={"collector": logger.collector},
    )
    logger.log_baml()
    t.print_timing("green")

    return AIMessage(
        content=combined_response.combinedText,
        additional_kwargs={
            "agent_classification": AgentTypes.FLIGHTS,
            "step": FlightPlanningStep.FLIGHT_CHECKOUT.value,
            "miss_info": miss_info,
        },
    )


async def seat_selection(
    *,
    user: User,
    spotnana_itinerary_id: str | None,
    spotnana_search_id: str | None,
    preferred_seat_types: list[str],
    company_allow_paid_seat: bool | None,
) -> Tuple[str, list[dict[str, Any]], dict[str, list[SeatSelectionForFlight]]]:
    try:
        assert spotnana_search_id is not None, "spotnana_search_id is required for seat selection"
        assert spotnana_itinerary_id is not None, "spotnana_itinerary_id is required for seat selection"
        itinerary = await FlightSearchTools.get_selected_itinerary_spotnana(spotnana_search_id, spotnana_itinerary_id)

        flights = FlightSearchTools.map_flights_in_itin(itinerary.get("itinerary", {}))

        user_profile_flights_loyalty_programs: dict[str, Any] | None = await get_user_profile_flights_loyalty_programs(
            user.id
        )

        stored_freq_flyer_ids: dict[str, str] = {}

        if user_profile_flights_loyalty_programs is not None:
            stored_freq_flyer_ids = (
                {
                    program["IATACode"]: program["number"]
                    for program in user_profile_flights_loyalty_programs.get("loyaltyPrograms", [])
                }
                if user_profile_flights_loyalty_programs.get("loyaltyPrograms", []) is not None
                else {}
            )

        loyaltyInfos = []
        if len(stored_freq_flyer_ids) > 0:
            for current_flight in flights:
                outbound_freq_flyer_number_airline = FlightSearchTools.freq_flyer_airline_code(
                    current_flight.get("airline_code"), stored_freq_flyer_ids
                )

                if outbound_freq_flyer_number_airline in stored_freq_flyer_ids:
                    flight_id = current_flight.get("flight_id")
                    loyaltyInfos.append(
                        {
                            "flightId": flight_id,
                            "loyaltyInfos": [
                                {
                                    "id": stored_freq_flyer_ids[outbound_freq_flyer_number_airline],
                                    "issuedBy": outbound_freq_flyer_number_airline,
                                    "type": "AIR",
                                }
                            ],
                        }
                    )

        t = Timings("FlightSearchTools: traveler_seat_map spotnana")
        traveler_search = await FlightSearchTools.traveler_search_spotnana(user.email)
        user_guid = None
        if len(traveler_search["results"]) == 0:
            # now just create user by providing minimal information.
            user_guid = await update_spotnana_user_profile(
                CreateSpotnanaProfile(
                    first_name=user.first_name or "unknown",
                    last_name=user.last_name or "unknown",
                    email=user.email,
                ),
                user.email,
                None,
            )
        else:
            traveler_read = await FlightSearchTools.traveler_read_spotnana(traveler_search["results"][0]["id"])
            user_guid = traveler_read.get("traveler", {}).get("userOrgId", {}).get("userId", {}).get("id")

        seat_maps = await FlightSearchTools.seat_map_spotnana(
            searchId=spotnana_search_id,
            itineraryId=spotnana_itinerary_id,
            loyaltyInfos=loyaltyInfos,
            user_guid=user_guid,
        )
        t.print_timing("green")

    except Exception as e:
        logger.error(f"Error when getting seat map from Spotnana: {e}")
        error_msg = FunctionMessage(content=str(e.args[0]), name="SeatSelection")
        error_msg.additional_kwargs = {
            "function_call": {
                "arguments": {"error_response": e.args[0]},
                "name": "Error Message",
            }
        }
        return "There is error get the seat map.", [], {}

    selected_seats = {}
    for_summary_seat_selection = []
    agent_response = ""
    for index, flight in enumerate(flights):
        flight_seat_map_ids = seat_maps.get("travelerSeatMaps", [])[0].get("flightSeatMapIds", [])
        flight_seat_map_id = flight_seat_map_ids[index]
        seat_map_list = [seat for seat in seat_maps.get("seatMaps") if seat.get("seatMapId") == flight_seat_map_id]

        seat_map_csv = ""
        seat_price_dict = {}
        if len(seat_map_list) == 0 or len(seat_map_list[0].get("cabinSections", [])) == 0:
            seat_map_csv = ""
        else:
            seat_map_csv, seat_price_dict = FlightSearchTools.convert_seat_map_to_csv(seat_map_list[0])

        t = Timings("BAML: DoSeatSelection")
        flight_id = flight.pop("flight_id", None)

        if not seat_map_csv:
            result = SeatMatchingResult(
                free_seat=None,
                paid_seat=None,
                agent_response="For this flight, the airline does not allow people to choose their seats. Do you want to go to next step?",
                seat_selection_reason="",
            )
        else:
            result = await b.DoSeatSelection(
                seat_map=seat_map_csv,
                preferred_seat=",".join(preferred_seat_types),
                current_flight=json.dumps(flight),
                all_flights="",
                next_flight=None,
                allow_paid_seat=company_allow_paid_seat,
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("green")
            logger.log_baml()

        if result.free_seat:
            selected_seats.setdefault(flight_id, []).append(
                SeatSelectionForFlight(
                    seat_number=result.free_seat,
                    price=0,
                    start_airport=flight.get("origin", {}).get("airportCode"),
                    end_airport=flight.get("destination", {}).get("airportCode"),
                )
            )
            flight_and_seat = {
                **flight,
                "seat_number": result.free_seat,
                "reason": result.seat_selection_reason,
            }
            for_summary_seat_selection.append(flight_and_seat)
        if result.paid_seat:
            selected_seats.setdefault(flight_id, []).append(
                SeatSelectionForFlight(
                    seat_number=result.paid_seat,
                    price=seat_price_dict.get(result.paid_seat),
                    start_airport=flight.get("origin", {}).get("airportCode"),
                    end_airport=flight.get("destination", {}).get("airportCode"),
                )
            )
            flight_and_seat = {
                **flight,
                "seat_number": result.paid_seat,
                "price": seat_price_dict.get(result.paid_seat),
                "reason": result.seat_selection_reason,
            }
            for_summary_seat_selection.append(flight_and_seat)
        agent_response += (
            f"Flight ({flight.get('airline_code')}){flight.get('flight_number')}  - {result.agent_response} \n"
        )

    t = Timings("BAML: SummarizeSeatSelectionResponse")
    summarized_response = await b.SummarizeSeatSelectionResponse(
        agent_response=agent_response,
        self_intro=settings.OTTO_SELF_INTRO,
        convo_style=settings.OTTO_CONVO_STYLE,
        baml_options={"collector": logger.collector},
    )
    t.print_timing("green")
    logger.log_baml()
    return summarized_response, for_summary_seat_selection, selected_seats


@trace
async def flight_credits_check(
    fh_executor: "fhe.TripPlanExecutor",
    outbound_flight: FlightOption,
    return_flight: Optional[FlightOption],
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
):
    user_unused_credits = await flight_credits_api.get_user_unused_credits(fh_executor.user.email)
    ariline_names = []
    for stop in outbound_flight.stops:
        ariline_names.append(stop.airline_name)
    if return_flight:
        for stop in return_flight.stops:
            ariline_names.append(stop.airline_name)

    credits_details: FlightCreditsResponse = await b.ProcessApplyFlightCreditsConfirmation(
        selected_flights=ariline_names,
        credits=json.dumps(user_unused_credits),
        current_date=get_current_date_string(fh_executor.timezone),
        self_intro=settings.OTTO_SELF_INTRO,
        convo_style=settings.OTTO_CONVO_STYLE,
        baml_options={"collector": logger.collector},
    )
    logger.log_baml()

    function_message = FunctionMessage(
        content=credits_details.agent_response,
        name=credits_details.__class__.__name__,
        additional_kwargs={
            "function_call": {
                "name": credits_details.__class__.__name__,
                "arguments": credits_details.model_dump_json(),
            },
            "agent_classification": AgentTypes.FLIGHTS,
            "step": FlightPlanningStep.FLIGHT_CREDIT_CHECK.value,
        },
    )

    await message_persistor([function_message])
    message = await map_websocket_message(
        function_message,
        None,
        None,
    )
    await fh_executor.message_sender(message={**message[0]})
    return function_message


async def validate_flight_search_params(
    fh_executor: "fhe.TripPlanExecutor", flight_search_core: FlightSearchCoreCriteria
):
    validate_list = []
    if not flight_search_core.departure_airport_code:
        validate_list.append("departure airport code or city is missing")
    if not flight_search_core.arrival_airport_code:
        validate_list.append("arrival airport code or city is missing")
    if flight_search_core.departure_airport_code == flight_search_core.arrival_airport_code:
        validate_list.append(
            "looks like you're already in the destination given departure and arrival airport code or city are the same. is that right?"
        )

    if not flight_search_core.outbound_date:
        validate_list.append("departure date is missing")
    elif not is_valid_future_date(flight_search_core.outbound_date, fh_executor.timezone):
        validate_list.append(
            "A past date is provided for the outbound date. I won't be able to search for past flights."
        )

    if not flight_search_core.flight_type:
        validate_list.append("flight type is missing, is it one way or round trip?")

    if flight_search_core.flight_type == FlightType.RoundTrip:
        if not flight_search_core.return_date:
            validate_list.append("return date is missing for round trip")
        elif not is_valid_future_date(flight_search_core.return_date, fh_executor.timezone):
            validate_list.append(
                "A past date is provided for the return date. I won't be able to search for past flights."
            )
        elif not is_valid_future_date(
            flight_search_core.return_date, fh_executor.timezone, flight_search_core.outbound_date
        ):
            validate_list.append(
                "The return date is earlier than the outbound date. I can't search for round trip flight this way."
            )

    if validate_list:
        combined_response = await b.RephraseRequestForMissingOrInvalidInfo(
            sentences=[
                "Looks like there're something to clarify about your flight before I could proceed.",
                *validate_list,
            ],
            current_date=get_current_date_string(fh_executor.timezone),
            user_name=fh_executor.user.name,
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            need_response_with_name=False,
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()
        return AIMessage(
            content=combined_response.combinedText,
            additional_kwargs={
                "agent_classification": AgentTypes.FLIGHTS,
            },
        )
    return None


async def check_payment_profile_message(user_id: int) -> AIMessage | None:
    """
    Check if the user has a valid payment profile and create a message if not.

    Args:
        user_id: The user's ID

    Returns:
        Optional[AIMessage]: The message to send if the profile is invalid, None if the profile is valid
    """
    user = await UserDB.from_id(user_id)
    assert user is not None, f"User {user_id} not found"

    company_admin_user: UserDB | None = None
    if user.organization_id:
        company_admin_user = await UserDB.from_organization_id_and_role(user.organization_id, UserRole.company_admin)

    get_user_profile_tasks = [
        get_user_profile_personal_information(user_id),
        get_user_profile_payment_information(user_id),
    ]
    if company_admin_user:
        get_user_profile_tasks.append(get_user_profile_payment_information(company_admin_user.id))

    (user_profile_personal_information, user_profile_payment_information, *rest) = await asyncio.gather(
        *get_user_profile_tasks
    )
    admin_payment_information = rest[0] if company_admin_user else None

    has_personal_profile, missing_personal_info_fields = get_missing_user_profile_personal_information_fields(
        user_profile_personal_information
    )
    has_payment_profile, missing_payment_info_fields = get_missing_user_profile_payment_information_fields(
        user_profile_payment_information
    )
    has_admin_payment_profile, missing_admin_payment_info_fields = get_missing_user_profile_payment_information_fields(
        admin_payment_information
    )
    has_any_completed_payment_profile = (
        has_payment_profile and missing_payment_info_fields is not None and len(missing_payment_info_fields) == 0
    ) or (
        has_admin_payment_profile
        and missing_admin_payment_info_fields is not None
        and len(missing_admin_payment_info_fields) == 0
    )
    has_completed_personal_profile = (
        has_personal_profile and missing_personal_info_fields is not None and len(missing_personal_info_fields) == 0
    )

    if has_any_completed_payment_profile and not has_completed_personal_profile:
        new_message = AIMessage(
            content="I don't have your personal information on file so I can't reserve your flight. Please provide these details now so I can reserve your flight."
        )
        new_message.additional_kwargs = {
            "agent_classification": AgentTypes.FLIGHTS,
        }
        return new_message
    elif has_completed_personal_profile and not has_any_completed_payment_profile:
        new_message = AIMessage(
            content="I don't have your payment information on file so I can't reserve your flight. Please provide these details now so I can reserve your flight."
        )
        new_message.additional_kwargs = {
            "agent_classification": AgentTypes.FLIGHTS,
        }
        return new_message
    elif not has_any_completed_payment_profile and not has_completed_personal_profile:
        what_we_have = ["personal"] if has_personal_profile else []
        what_we_have += ["payment"] if has_payment_profile else []
        what_we_have = " and ".join(what_we_have)
        what_missing = (missing_payment_info_fields or [])[:1] + (missing_payment_info_fields or [])[:1]
        new_message = AIMessage(
            content=f"I have some of your {what_we_have or 'personal'} information, "
            f"but I'm missing a few required fields{' like' + ', '.join(what_missing) + ', etc' if what_missing else ''}. "
            f"Please provide these details now so I can reserve your flight."
        )
        new_message.additional_kwargs = {
            "agent_classification": AgentTypes.FLIGHTS,
        }
        return new_message

    return None


@trace
async def flight_booking_v2(
    fh_executor: "fhe.TripPlanExecutor",
    outbound_flight: FlightOption,
    return_flight: Optional[FlightOption],
    selected_flight_for_segment: Optional[Dict[str, FlightOption]],
    user_responsed_entry_requirment: bool | None,
    user_provided_citizenship: list[str] | None,
    is_international: bool,  # thiis is inferred by LLM.
    preferred_seat_types: list[str],
    validation_callhandler: Callable | None,
    flight_select_result: EnrichedFlightSelectResult,
    seat_selection_for_flight: list[SeatSelectionForFlight],
    departure_airport_code: str | None,
    arrival_airport_code: str | None,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    booking_callhandler: Callable | None,
    flight_search_source: FlightSearchSource | None,
    spotnana_search_id: str | None,
    spotnana_flight_id: str | None,
):
    logger.info(f"Booking flight with criteria: {flight_select_result.model_dump()}", flight_mask)

    # Check if user has valid payment profile
    new_message = await check_payment_profile_message(fh_executor.user.id)
    if new_message:
        await message_persistor([new_message])
        message = await map_websocket_message(
            new_message,
            None,
            None,
        )
        await fh_executor.message_sender(message={**message[0]})
        await fh_executor.message_sender(
            message={
                "type": "open_flights_payment_form",
            }
        )
        return new_message

    fh_executor.schedule_send_message(
        message={
            "type": "search_update",
            "text": "I'm booking your flight now and it typically takes 2 mins — hold tight while I confirm all the details for you...",
            "isBotMessage": True,
            "expectResponse": False,
        }
    )

    if not user_responsed_entry_requirment:
        if is_international:
            logger.info(
                f"ask for checking entry requirement for {fh_executor.user.name} with citizenship {user_provided_citizenship}",
                flight_mask,
            )
            message_content = None
            if not fh_executor.user.citizenship and not user_provided_citizenship:
                message_content = "Before you confirm your booking, please make sure you have the necessary visas or travel documents for your destination—Otto can't guarantee entry or validate travel permissions—but I can help by checking the entry requirements for your citizenship. Want me to take a look? what is your citizenship?"

            else:
                message_content = "Before you confirm your booking, please make sure you have the necessary visas or travel documents for your destination—Otto can't guarantee entry or validate travel permissions—but I can help by checking the entry requirements for your citizenship. Want me to take a look?"

            ask_for_citizen_message = AIMessage(
                content=message_content,
                additional_kwargs={
                    "agent_classification": AgentTypes.FLIGHTS,
                },
            )
            await message_persistor([ask_for_citizen_message])
            message = await map_websocket_message(
                ask_for_citizen_message,
                None,
                None,
            )
            await fh_executor.message_sender(message={**message[0]})
            return message

    flight_credit_number = None
    if (
        flight_select_result.flight_credits_ticket_numbers
        and len(flight_select_result.flight_credits_ticket_numbers) > 0
    ):
        flight_credit_number = flight_select_result.flight_credits_ticket_numbers[0]

    spotnana_final_flight_id = None
    if flight_select_result.selected_flight_for_segment:
        spotnana_final_flight_id = flight_select_result.selected_flight_for_segment[-1].selected_flight_id

    company_admin_user: UserDB | None = None
    if fh_executor.user.organization_id:
        company_admin_user = await UserDB.from_organization_id_and_role(
            fh_executor.user.organization_id, UserRole.company_admin
        )

    (
        response_json,
        booking_id,
        initial_booking_id,
        trip_id,
    ) = await FlightSearchTools.flight_validation_spotnana_simplified(
        flight_params_dict={
            "search_id": flight_select_result.search_id,
            "user_email": fh_executor.user.email,
            "user_id": fh_executor.user.id,
            "selected_outbound_flight_id": flight_select_result.matched_flight_id
            or flight_select_result.selected_outbound_flight_id
            or spotnana_final_flight_id,
            "selected_return_flight_id": flight_select_result.matched_flight_id
            or flight_select_result.selected_return_flight_id
            or spotnana_final_flight_id,
            "seat_selection": [seat_info.model_dump() for seat_info in seat_selection_for_flight],
            "arrival_airport_code": arrival_airport_code,
            "credits_ticket_number": flight_credit_number,
            "preferred_seat_types": preferred_seat_types,
        },
        user=fh_executor.user,
        admin_user=company_admin_user,
        websocket_send_message=fh_executor.message_sender,
    )

    response = json.loads(response_json)
    if response.get("status") == "error_no_flight_payment_profile_found":
        await fh_executor.message_sender(
            message={
                "type": "open_flights_payment_form",
            }
        )
        return

    if response.get("status") == "ITINERARY_FARE_EXPIRED":
        if flight_select_result.search_source == FlightSearchSource.FALLBACK_SPOTNANA:
            new_message = AIMessage(
                content="Your selected flight fare has expired. Please try searching again. Sorry for the inconvenience.",
            )
            new_message.additional_kwargs = {
                "agent_classification": AgentTypes.FLIGHTS,
            }
            await message_persistor([new_message])
            message = await map_websocket_message(
                new_message,
                None,
                None,
            )
            await fh_executor.message_sender(message={**message[0]})
            return new_message
        fh_executor.schedule_send_message(
            message={
                "type": "search_update",
                "text": "I'm sorry, the fare for the selected itinerary has expired. let me try again...",
                "isBotMessage": True,
                "expectResponse": False,
            }
        )
        re_checkout_message = await flight_checkout(
            fh_executor,
            outbound_flight,
            return_flight,
            selected_flight_for_segment,
            flight_select_result,
            preferred_seat_types,
            validation_callhandler,
            message_persistor,
            flight_search_source=flight_search_source,
            spotnana_search_id=spotnana_search_id,
            spotnana_flight_id=spotnana_flight_id,
        )
        return re_checkout_message

    if booking_id and trip_id:
        fh_executor.schedule_send_message(
            message={
                "type": "search_update",
                "text": "I'm now creating PNR for your booking...",
                "isBotMessage": True,
                "expectResponse": False,
            }
        )

        create_pnr = await FlightSearchTools.create_pnr_spotnana(
            bookingId=booking_id,
            initiateBookingId=initial_booking_id,
            tripId=trip_id,
        )

        trip_details = await FlightSearchTools.get_trip_details_spotnana(trip_id)

        airline_confirmation_number = FlightSearchTools.get_vendor_confirmation_number(trip_details)
        create_pnr.update({"airline_confirmation_number": airline_confirmation_number})
        create_pnr.update({"trip_id": trip_id})

        if response.get("seat_validation_messages"):
            create_pnr["seat_change_info"] = response.get("seat_validation_messages")

        results = await b.ProcessFlightBookingResults(
            results=json.dumps(create_pnr),
            airline_confirmation_number=airline_confirmation_number,
            current_date=get_current_date_string(fh_executor.timezone),
            self_intro=settings.OTTO_SELF_INTRO,
            convo_style=settings.OTTO_CONVO_STYLE,
            baml_options={"collector": logger.collector},
        )
        logger.log_baml()

        arguments_obj = results.model_dump()

        # stuff tool responses back into model being returne
        arguments_obj["trip_id"] = trip_id

        arguments_obj["status"] = create_pnr.get("status")
        arguments_obj["airline_confirmation_number"] = create_pnr.get("airline_confirmation_number")
        arguments_obj["confirmation_id"] = create_pnr.get("pnrId")
        arguments_obj["source_pnr_id"] = create_pnr.get("pnrNumber")

        function_message = FunctionMessage(
            content="",
            name=results.__class__.__name__,
            additional_kwargs={
                "function_call": {
                    "name": results.__class__.__name__,
                    "arguments": json.dumps(arguments_obj),
                },
                "agent_classification": AgentTypes.FLIGHTS,
            },
        )

        await message_persistor([function_message])
        message = await map_websocket_message(
            function_message,
            None,
            None,
        )
        await fh_executor.message_sender(message={**message[0]})

        if (
            booking_callhandler
            and trip_id
            and create_pnr.get("pnrId")
            and create_pnr.get("airline_confirmation_number")
        ):
            await booking_callhandler(trip_id, create_pnr.get("pnrId"), create_pnr.get("airline_confirmation_number"))

        selected_itin = await FlightSearchTools.get_selected_itinerary_spotnana(
            searchId=flight_select_result.search_id or "",
            itineraryId=flight_select_result.matched_flight_id or spotnana_final_flight_id or "",
        )

        total_seat_price = sum(seat_selection.price or 0 for seat_selection in seat_selection_for_flight) or None
        flat_flight_options = FlightSearchTools.flattenFlights_itin(selected_itin["itinerary"], None, total_seat_price)

        # invalidate flight credits cache if there are any flight credits used
        if flight_credit_number:
            asyncio.create_task(flight_credits_api.invaliate_mongo_cache(fh_executor.user.email))

        booking = await save_flight_booking(
            fh_executor,
            trip_id,
            create_pnr.get("pnrId") or "",
            create_pnr.get("airline_confirmation_number") or "",
            flat_flight_options,
        )

        await fh_executor.send_post_booking_message(message_persistor)

        # add booking memory
        asyncio.create_task(
            BookingsMemory(
                user_id=str(fh_executor.user.id), thread_id=str(fh_executor.thread.id)
            ).store_flight_booking_memory(
                confirmation_id=create_pnr.get("pnrId") or "",
                airline_confirmation_number=create_pnr.get("airline_confirmation_number") or "",
                flight_booking_details=booking.get("content") or {},
                operation=BookingOperation.BOOKED,
            )
        )

        return function_message


@trace
async def flight_checkout(
    fh_executor: "fhe.TripPlanExecutor",
    outbound_flight: FlightOption,
    return_flight: Optional[FlightOption],
    selected_flight_for_segment: Optional[Dict[str, FlightOption]],
    flight_select_result: EnrichedFlightSelectResult,
    preferred_seat_types: list[str],
    callback_handler: Callable | None,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    flight_search_source: FlightSearchSource | None,
    spotnana_search_id: str | None,
    spotnana_flight_id: str | None,
):
    validation_message = await checking_requirment_before_validation(
        fh_executor, preferred_seat_types, [outbound_flight.stops[0].airline_code]
    )
    if validation_message:
        await message_persistor([validation_message])
        message = await map_websocket_message(
            validation_message,
            None,
            None,
        )
        await fh_executor.message_sender(message={**message[0]})
        return validation_message
    fh_executor.schedule_send_message(
        message={
            "type": "search_update",
            "text": "I'm checking out your selected flights now and it might take a minute or two — hold tight while I confirm all the details for you...",
            "isBotMessage": True,
            "expectResponse": False,
        }
    )

    (
        auto_proceed_msg,
        response,
        spotnana_search_id,
        spotnana_flight_id,
        seat_to_store,
    ) = await flight_checkout_with_match(
        fh_executor,
        outbound_flight,
        return_flight,
        selected_flight_for_segment,
        flight_select_result,
        preferred_seat_types,
        flight_search_source,
        spotnana_search_id,
        spotnana_flight_id,
    )

    if (spotnana_search_id or spotnana_flight_id) and callback_handler:
        await callback_handler(spotnana_search_id, spotnana_flight_id, list(seat_to_store.values()))

    msg_to_persist: list[BaseMessage] = []
    if auto_proceed_msg:
        msg_to_persist.append(auto_proceed_msg)
    msg_to_persist.append(response)

    await message_persistor(msg_to_persist)

    message = await map_websocket_message(
        response,
        None,
        None,
    )
    await fh_executor.message_sender(message={**message[0]})

    return response


async def fallback_spotnana_search(
    fh_executor: "fhe.TripPlanExecutor",
    flight_search_criteria: FlightSearchParams,
    message_buffer_str: list[str],
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    callback_handler: Callable | None,
    is_premium_cabin_search: bool,
    airport_code: str,
    current_segment_index: int | None,
):
    travel_context_dict: dict[str, Any] = json.loads(flight_search_criteria.get("travel_context") or "{}")
    logger.info(f"Searching flights on Spotnana with the following criteria: {flight_search_criteria}", flight_mask)

    spotnana_raw_data = await FlightSearchTools.search_flights_spotnana(
        flight_search_criteria, current_segment_index, fh_executor.schedule_send_message
    )
    return await process_spotnana_search_results(
        fh_executor,
        spotnana_raw_data,
        message_buffer_str,
        travel_context_dict,
        flight_search_criteria,
        is_premium_cabin_search,
        airport_code,
        callback_handler,
        message_persistor,
    )


async def process_spotnana_search_results(
    fh_executor: "fhe.TripPlanExecutor",
    spotnana_raw_data: dict[str, Any],
    message_buffer_str: list[str],
    travel_context_dict: dict[str, Any],
    flight_search_criteria: FlightSearchParams,
    is_premium_cabin_search: bool,
    airport_code: str,
    callback_handler: Callable | None,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
):
    function_message, search_id, has_flights = await fh_executor.flight_helper.proccess_flight_search_results(
        flight_response=spotnana_raw_data,
        message_buffer_strs=message_buffer_str,
        travel_context_dict=travel_context_dict,
        is_return_search=flight_search_criteria["current_step"] == "RETURN_FLIGHT_SEARCH",
        is_premium_cabin_search=is_premium_cabin_search,
        airport_code=airport_code,
        default_airline_codes=flight_search_criteria["default_airline_brands"] or [],
        preferred_airline_codes=flight_search_criteria["preferred_airline_codes"] or [],
    )

    if isinstance(function_message, AgentError):
        return AIMessage(
            content="There was an error while searching for flights. Please try again later.",
        )
    else:
        function_message.additional_kwargs["step"] = (
            FlightPlanningStep.RETURN_FLIGHT_SEARCH.value
            if flight_search_criteria["current_step"] == "RETURN_FLIGHT_SEARCH"
            else FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.value
        )

        await message_persistor([function_message])

        messages = await map_websocket_message(
            function_message,
            None,
            None,
        )

        await fh_executor.message_sender(message={**messages[0]})
        if has_flights:
            await _prompt_to_update_preferences(flight_search_criteria, fh_executor, message_persistor)
        return function_message


def __determine_search_source_for_outbound_search(
    current_segment_index: int | None,
    existing_flight_search_source: FlightSearchSource | None,
    flight_search_criteria_additional: FlightSearchAdditionalCriteria,
):
    if current_segment_index is not None:
        return FlightSearchSource.FALLBACK_SPOTNANA
    if flight_search_criteria_additional.refundable or flight_search_criteria_additional.upgrade:
        return FlightSearchSource.FALLBACK_SPOTNANA
    if existing_flight_search_source == FlightSearchSource.FALLBACK_SPOTNANA:
        return FlightSearchSource.FALLBACK_SPOTNANA
    return FlightSearchSource.SERP


@trace
async def search_flights(
    fh_executor: "fhe.TripPlanExecutor",
    is_outbound_search: bool,
    flight_search_criteria: FlightSearchParams,
    message_buffer_str: list[str],
    flight_search_type: FlightSearchType,
    flight_search_criteria_additional: FlightSearchAdditionalCriteria,
    is_premium_cabin_search: bool,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    existing_flight_search_source: FlightSearchSource | None,
    current_segment_index: int | None,
    callback_handler: Callable | None = None,
):
    validation_message = await validate_flight_search_params(
        fh_executor, FlightSearchCoreCriteria.model_validate(flight_search_criteria)
    )
    if validation_message:
        await message_persistor([validation_message])
        message = await map_websocket_message(
            validation_message,
            None,
            None,
        )
        await fh_executor.message_sender(message={**message[0]})
        return validation_message
    assert flight_search_criteria["departure_airport_code"], "departure_airport_code is required"
    assert flight_search_criteria["arrival_airport_code"], "arrival_airport_code is required"
    # Give user a more accurate estimate of the search time based on the flight type
    search_estimation_seconds = 10 if flight_search_type == FlightSearchType.ONE_WAY else 30
    initial_search_message = f"I'm searching live flight inventory — this usually takes ~{search_estimation_seconds} seconds. Sit tight while I find the best options for you..."
    if flight_search_type == FlightSearchType.MULTI_CITY:
        initial_search_message = "This is a multi-leg trip, so it might take a minute or two to find the best options. Thanks for your patience — I'm on it!"
    fh_executor.schedule_send_message(
        message={
            "type": "flights_skeleton_async",
            "text": initial_search_message,
            "isBotMessage": True,
            "expectResponse": False,
        }
    )

    travel_context_dict: dict[str, Any] = json.loads(flight_search_criteria.get("travel_context") or "{}")

    serp_flight_options: list[Booking] | None = None
    choice: dict = {}
    airport_code = (
        flight_search_criteria["departure_airport_code"]
        if is_outbound_search
        else flight_search_criteria["arrival_airport_code"]
    )

    if is_outbound_search:
        search_source = __determine_search_source_for_outbound_search(
            current_segment_index,
            existing_flight_search_source,
            flight_search_criteria_additional,
        )

        if search_source == FlightSearchSource.FALLBACK_SPOTNANA:
            return await fallback_spotnana_search(
                fh_executor,
                flight_search_criteria,
                message_buffer_str,
                message_persistor,
                callback_handler,
                is_premium_cabin_search,
                airport_code,
                current_segment_index,
            )

        # SERP + SPOTNANA parrallel search
        serp_search_task = None
        spotnana_search_task = None
        spotnana_raw_data = None

        message_text = ""
        try:
            serp_search_task = asyncio.create_task(
                fh_executor.serp_flight_search.search_flights_serp(
                    flight_search_criteria, fh_executor.schedule_send_message
                )
            )

            spotnana_search_task = asyncio.create_task(
                FlightSearchTools.search_flights_spotnana(
                    flight_search_criteria,
                    current_segment_index,
                    None,  # spotnana search is in parrallel with SERP search, no need to send message
                )
            )

            try:
                choice, serp_flight_options = await serp_search_task

                if choice and serp_flight_options:
                    fh_executor.flight_choice_from_outbound_search = choice
                else:
                    asyncio.create_task(track_serp_not_found_metrics(fh_executor.user.id, fh_executor.thread.id))
                    logger.error("No outbound flights found in SERP", flight_mask)
                    message_text = "Looks like one inventory is dry, let me check the flight options from another live inventory for you..."
                    serp_flight_options = None
            except Exception as e:
                logger.error(f"SERP search failed: {str(e)}", flight_mask)
                message_text = "I'm searching deeper through an alternative flight inventory to find outbound flights for you, it might takes a couple of minutes — sit tight..."
                serp_flight_options = None

            if not serp_flight_options:
                fh_executor.schedule_send_message(
                    message={
                        "type": "flights_skeleton_async",
                        "text": message_text,
                        "isBotMessage": True,
                        "expectResponse": False,
                    }
                )

                try:
                    logger.info("Falling back to search flights on Spotnana.", flight_mask)
                    asyncio.create_task(track_fallback_spotnana_metrics(fh_executor.user.id, fh_executor.thread.id))
                    spotnana_raw_data = await spotnana_search_task
                    if spotnana_raw_data:
                        spotnana_message_text = f"I found {len(spotnana_raw_data['flight_choices'])} available flight from another live inventory. I'm now picking the best options for you based on your preferences..."

                        fh_executor.schedule_send_message(
                            message={
                                "type": "flights_skeleton_async",
                                "text": spotnana_message_text,
                                "isBotMessage": True,
                                "expectResponse": False,
                            }
                        )

                    return await process_spotnana_search_results(
                        fh_executor,
                        spotnana_raw_data,
                        message_buffer_str,
                        travel_context_dict,
                        flight_search_criteria,
                        is_premium_cabin_search,
                        airport_code,
                        callback_handler,
                        message_persistor,
                    )

                except Exception as e:
                    logger.error(f"Spotnana search failed: {str(e)}", flight_mask)
                    raise Exception("Spotnana search failed") from e

        except Exception as e:
            logger.error(f"Error during flight search: {str(e)}", flight_mask)
            empty_flight_message = AIMessage(content="Sorry but I couldn't find any available flight options.")
            await message_persistor([empty_flight_message])
            messages = await map_websocket_message(
                empty_flight_message,
                None,
                None,
            )
            await fh_executor.message_sender(message={**messages[0]})
            return empty_flight_message

    elif (
        flight_search_criteria.get("flight_type") == FlightType.RoundTrip
        or flight_search_criteria.get("flight_type") == FlightType.MultiLegs
    ):
        if (
            existing_flight_search_source is None or existing_flight_search_source == FlightSearchSource.SERP
        ) and current_segment_index is None:
            serp_flight_search_id = get_flight_search_id_for_return_search_from_serp_flight_id(
                flight_search_criteria.get("selected_outbound_flight_id")
            )
            assert serp_flight_search_id, "serp_flight_search_id is required for return flight search"
            _, serp_flight_options = await fh_executor.serp_flight_search.search_return_flights_serp(
                serp_flight_search_id,
                fh_executor.flight_choice_from_outbound_search or {},
                None
                if current_segment_index is None
                else current_segment_index == len(flight_search_criteria["search_segments"] or []) - 1,
            )
        else:
            return await fallback_spotnana_search(
                fh_executor,
                flight_search_criteria,
                message_buffer_str,
                message_persistor,
                callback_handler,
                is_premium_cabin_search,
                airport_code,
                current_segment_index,
            )

    if not serp_flight_options:
        asyncio.create_task(track_serp_not_found_metrics(fh_executor.user.id, fh_executor.thread.id))
        empty_flight_message = AIMessage(content="Sorry but I couldn't find any available flight options.")
        await message_persistor([empty_flight_message])
        messages = await map_websocket_message(
            empty_flight_message,
            None,
            None,
        )

        await fh_executor.message_sender(message={**messages[0]})
        return messages

    if is_outbound_search:
        travel_context_dict.pop("return_departure_time", None)
        travel_context_dict.pop("return_arrival_time", None)
    else:
        travel_context_dict.pop("outbound_departure_time", None)
        travel_context_dict.pop("outbound_arrival_time", None)

    fh_executor.schedule_send_message(
        message={
            "type": "flights_skeleton_async",
            "text": f"I found {len(serp_flight_options)} available flight options. I'm now picking the best options for you based on your preferences...",
            "isBotMessage": True,
            "expectResponse": False,
        }
    )

    function_message = await fh_executor.flight_helper.process_serp_flight_search_result(
        response=serp_flight_options,
        message_buffer_strs=[],
        travel_context_dict=travel_context_dict,
        is_return_search=flight_search_criteria.get("current_step") == "RETURN_FLIGHT_SEARCH",
        flight_search_type=flight_search_type,
        default_airline_codes=flight_search_criteria["default_airline_brands"] or [],
        preferred_airline_codes=flight_search_criteria["preferred_airline_codes"] or [],
        airport_code=flight_search_criteria["departure_airport_code"]
        if is_outbound_search
        else flight_search_criteria["arrival_airport_code"],
        is_premium_cabin_search=is_premium_cabin_search,
    )

    if isinstance(function_message, AgentError):
        return AIMessage(
            content="There was an error while searching for flights. Please try again later.",
        )
    else:
        function_message.additional_kwargs["step"] = (
            FlightPlanningStep.OUTBOUND_FLIGHT_SEARCH.value
            if is_outbound_search
            else FlightPlanningStep.RETURN_FLIGHT_SEARCH.value
        )
        await message_persistor([function_message])

        messages = await map_websocket_message(
            function_message,
            None,
            None,
        )

        await fh_executor.message_sender(message={**messages[0]})
        await _prompt_to_update_preferences(flight_search_criteria, fh_executor, message_persistor)
    return function_message


async def flight_baggage_check(
    fh_executor: "fhe.TripPlanExecutor",
    outbound_flight: FlightOption,
    return_flight: Optional[FlightOption],
    selected_flight_for_segment: Optional[Dict[str, FlightOption]],
    flight_select_result: EnrichedFlightSelectResult | None,
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
    callback_handler: Optional[Callable[[Any], Coroutine[Any, Any, None]]],
    flight_search_source: FlightSearchSource | None,
    spotnana_search_id: str | None,
    spotnana_flight_id: str | None,
):
    if flight_search_source == FlightSearchSource.FALLBACK_SPOTNANA:
        assert spotnana_search_id is not None, "spotnana_search_id is required for fallback search"
        assert spotnana_flight_id is not None, "spotnana_flight_id is required for fallback search"
        data = await FlightSearchTools.get_selected_itinerary_spotnana(spotnana_search_id, spotnana_flight_id)
    else:
        assert flight_select_result is not None, "flight_selected_result is required for baggage check"
        assert flight_select_result.search_id is not None, "spotnana_search_id is required for baggage check"
        assert flight_select_result.matched_flight_id is not None, "spotnana_flight_id is required for baggage check"
        data = await FlightSearchTools.get_selected_itinerary_spotnana(
            flight_select_result.search_id, flight_select_result.matched_flight_id
        )

    has_free_checked_baggage = False

    if data.get("itinerary"):
        legs = data.get("itinerary").get("legs", [])
        for leg in legs:
            leg_info_for_traveler = leg.get("travelerInfos", [])[0]
            fare_rules = leg_info_for_traveler.get("fareRules")
            if fare_rules.get("baggagePolicy"):
                policy = fare_rules.get("baggagePolicy")
                if policy and policy.get("checkedIn"):
                    for option in policy.get("checkedIn", []):
                        for fee in option.get("fee", []):
                            if (fee.get("fee", {}).get("amount") or 0) == 0:
                                has_free_checked_baggage = True
                                break

    # Default to allowing prepaid baggage
    allow_prepaid_baggage = True
    baggage_options = []

    if not has_free_checked_baggage:
        company_admin = (
            await UserDB.from_organization_id_and_role(fh_executor.user.organization_id, UserRole.company_admin)
            if fh_executor.user.organization_id
            else None
        )
        policy_user_id = company_admin.id if company_admin else fh_executor.user.id

        company_policy = await UserCompanyTravelPolicy.from_user_id(policy_user_id)

        if company_policy and company_policy.parsed_travel_policy:
            flight_policy_dict = company_policy.parsed_travel_policy.get("flight_policy")
            if flight_policy_dict:
                allow_prepaid_baggage = flight_policy_dict.get("allow_prepaid_baggage")

        if allow_prepaid_baggage is None or allow_prepaid_baggage:
            search_id = spotnana_search_id
            if flight_select_result is not None:
                search_id = flight_select_result.search_id

            flight_id = spotnana_flight_id
            if flight_select_result is not None:
                flight_id = flight_select_result.matched_flight_id

            checkout_data = None
            if search_id is not None and flight_id is not None:
                checkout_data = await FlightSearchTools.flight_checkout_spotnana(search_id, flight_id)

            if checkout_data and "baggageInfo" in checkout_data:
                baggage_options = checkout_data.get("baggageInfo", {}).get("baggageSelectionGroups", [])

    response_obj = await b.GenerateBaggageUpsellOffer(
        baggage_options=json.dumps(baggage_options),
        current_date=get_current_date_string(fh_executor.timezone),
        self_intro=settings.OTTO_SELF_INTRO,
        convo_style=settings.OTTO_CONVO_STYLE,
        baml_options={"collector": logger.collector},
    )
    response = response_obj.agent_response
    logger.log_baml()

    function_message = FunctionMessage(
        content="",
        name="FlightBaggageCheck",
        additional_kwargs={
            "function_call": {
                "name": "FlightBaggageCheck",
                "arguments": json.dumps({"agent_response": response, "baggage_options": baggage_options}),
            },
            "agent_classification": AgentTypes.FLIGHTS,
        },
    )
    await message_persistor([function_message])

    message = await map_websocket_message(
        function_message,
        None,
        None,
    )
    await fh_executor.message_sender(message={**message[0]})


async def _prompt_to_update_preferences(
    flight_search_criteria: FlightSearchParams,
    fh_executor: "fhe.TripPlanExecutor",
    message_persistor: Callable[[list[BaseMessage]], Coroutine[Any, None, None]],
):
    response = await b.PromptUserToUpdatePreferencesAfterSearch(
        "flight", search_criteria=str(flight_search_criteria), preferences=None
    )
    if response:
        msg = AIMessage(
            content=response,
            additional_kwargs={"agent_classification": AgentTypes.PREFERENCES},
        )
        await message_persistor([msg])

        messages = await map_websocket_message(msg)

        await fh_executor.message_sender(message={**messages[0]})
