import asyncio
import json
from dataclasses import dataclass
from datetime import <PERSON><PERSON><PERSON>
from enum import Enum
from functools import partial
from typing import Any, Callable, Dict, List, Optional, Tuple

from baml_client.types import FlightType
from flight_agent.flights_tools import airline_alliance_id_mapping
from front_of_house_agent import flight_utils
from front_of_house_agent.common_models import FlightSearchParams, FlightStop, SerpFlightOption
from front_of_house_agent.flight_utils import (
    SerpRequestParam,
    generate_serp_flight_option_id,
    get_timezone_from_iata_airport_code,
    minutes_to_iso_duration,
)
from front_of_house_agent.serp_common_models import Booking, Flight, SerpFlightParser, Stop
from llm_utils.llm_utils import csv_from_dict, determine_time_category
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User
from server.utils.cloudwatch_metrics import metrics_service
from server.utils.google_serp_api import google_serp_api
from server.utils.logger import logger
from server.utils.remote_cache import RemoteCache
from server.utils.settings import settings
from virtual_travel_agent.helpers import console_masks
from virtual_travel_agent.timings import Timings


@dataclass(frozen=True)
class Airport:
    TIME = "time"
    ID = "id"
    NAME = "name"


class TripType(Enum):
    ROUNDTRIP = 1
    ONEWAY = 2
    MULTI_LEGS = 3


class SortBy(Enum):
    TOP_FLIGHTS = 1
    PRICE = 2
    DEPARTURE_TIME = 3
    ARRIVAL_TIME = 4
    DURATION = 5
    EMISSIONS = 6


class TravelClass(Enum):
    ECONOMY = 1
    PREMIUM_ECONOMY = 2
    BUSINESS = 3
    FIRST = 4

    @classmethod
    def from_value(cls, value: Optional[str]) -> "TravelClass":
        # Default to ECONOMY if None
        if value is None:
            return cls.ECONOMY

        # Dictionary simulating the switch-case
        case_map = {
            "economy": cls.ECONOMY,
            "premium economy": cls.ECONOMY,  # Consider both economy and premium economy as ECONOMY
            "business": cls.BUSINESS,
            "first": cls.FIRST,
        }

        # Convert value to lowercase and check the dictionary
        return case_map.get(value.lower(), cls.ECONOMY)


class NumOfStops(Enum):
    ANY = 0
    NON_STOP = 1
    ONE_STOP = 2
    TWO_STOPS = 3

    @classmethod
    def from_value(cls, value: int) -> "NumOfStops":
        """Maps an integer to the corresponding NumOfStops enum value."""
        mapping = {0: cls.NON_STOP, 1: cls.ONE_STOP, 2: cls.TWO_STOPS}
        return mapping.get(value, cls.ANY)  # Returns any if value is not in mapping


BOOKING_TOKEN_TABLE_PREFIX = "booking_token"
DEPARTURE_TABLE_PREFIX = "departure_token"
SERP_SEARCH_PARAM_TABLE_PREFIX = "serp_search_param"
MIN_FLIGHT_SEARCH_WINDOW = 4  # hours

NON_EXCHANGABLE_FARES_MAP: Dict[str, List[str]] = {
    "AA": ["basic economy"],  # American Airlines
    "DL": ["basic economy"],  # Delta Air Lines
    "UA": ["basic economy"],  # United Airlines
    "B6": ["blue basic"],  # JetBlue Airways
    "AS": ["saver"],  # Alaska Airlines
    "NK": ["go"],  # Spirit Airlines
    "F9": ["basic fare"],  # Frontier Airlines
    "HA": ["main cabin basic"],  # Hawaiian Airlines
}

FALLBACK_THRESHOLD: int = settings.SERP_FALLBACK_THRESHOLD

serp_flight_log_mask = console_masks["flight"]


def inspect(request_id: str) -> str:
    return f"https://serpapi.com/searches/{request_id}/inspect"


class SerpFlightSearchParamBuilder:
    def __init__(
        self,
        flight_params: FlightSearchParams,
        currency: str = "USD",
        language: str = "en",
        country: str = "us",
    ):
        self.flight_params = flight_params
        self.currency = currency
        self.language = language
        self.country = country
        self.request_params: Dict[str, Any] = {}
        self.__base()

    def __base(self) -> "SerpFlightSearchParamBuilder":
        departure = flight_utils.get_with_assert(self.flight_params, "departure_airport_code")
        if self.flight_params["is_departure_iata_city_code"]:
            if departure in settings.METRO_AREA_CODES:
                departure = ",".join(settings.METRO_AREA_CODES.get(departure, [departure]))
            else:
                logger.info(f"Departure city code {departure} is not in metro area code", mask=serp_flight_log_mask)

        arrival = flight_utils.get_with_assert(self.flight_params, "arrival_airport_code")
        if self.flight_params["is_arrival_iata_city_code"]:
            if arrival in settings.METRO_AREA_CODES:
                arrival = ",".join(settings.METRO_AREA_CODES.get(arrival, [arrival]))
            else:
                logger.info(f"Arrival city code {arrival} is not in metro area code", mask=serp_flight_log_mask)

        outbound_date = flight_utils.get_with_assert(self.flight_params, "outbound_date")
        return_date = self.flight_params.get("return_date")

        self.request_params = {
            SerpRequestParam.CURRENCY: self.currency,
            SerpRequestParam.LANGUAGE: self.language,
            SerpRequestParam.COUNTRY: self.country,
            SerpRequestParam.SORT_BY: str(SortBy.TOP_FLIGHTS.value),
            SerpRequestParam.ASYNC: "false",
            SerpRequestParam.SHOW_HIDDEN: "false",
        }

        if self.flight_params["flight_type"] == FlightType.MultiLegs:
            segments = []
            for search_segment in self.flight_params["search_segments"] or []:
                segment_dict = {}
                departure = flight_utils.get_with_assert(search_segment, "departure_airport_code")
                if search_segment.is_departure_iata_city_code:
                    if departure in settings.METRO_AREA_CODES:
                        departure = ",".join(settings.METRO_AREA_CODES.get(departure, [departure]))
                    else:
                        logger.info(
                            f"Departure city code {departure} is not in metro area code", mask=serp_flight_log_mask
                        )

                arrival = flight_utils.get_with_assert(search_segment, "arrival_airport_code")
                if search_segment.is_arrival_iata_city_code:
                    if arrival in settings.METRO_AREA_CODES:
                        arrival = ",".join(settings.METRO_AREA_CODES.get(arrival, [arrival]))
                    else:
                        logger.info(f"Arrival city code {arrival} is not in metro area code", mask=serp_flight_log_mask)

                outbound_date = flight_utils.get_with_assert(search_segment, "outbound_date")
                return_date = search_segment.return_date
                segment_dict = {
                    SerpRequestParam.DEPARTURE_ID: departure,
                    SerpRequestParam.ARRIVAL_ID: arrival,
                    SerpRequestParam.DATE: outbound_date,
                }
                segments.append(segment_dict)
            self.request_params.update(
                {
                    SerpRequestParam.TYPE: TripType.MULTI_LEGS.value,
                    SerpRequestParam.MULTI_CITY_JSON: json.dumps(segments),
                }
            )
        else:
            self.request_params.update(
                {
                    SerpRequestParam.DEPARTURE_ID: departure,
                    SerpRequestParam.ARRIVAL_ID: arrival,
                    SerpRequestParam.OUTBOUND_DATE: outbound_date,
                }
            )

            if return_date:
                self.request_params[SerpRequestParam.RETURN_DATE] = return_date
                self.request_params[SerpRequestParam.TYPE] = TripType.ROUNDTRIP.value
            else:
                self.request_params[SerpRequestParam.TYPE] = TripType.ONEWAY.value

        return self

    @staticmethod
    def get_alliances_for_airlines(airline_codes: List[str]) -> List[str]:
        alliances = list(
            {
                airline_alliance_id_mapping[airline]
                for airline in airline_codes
                if airline in airline_alliance_id_mapping and airline_alliance_id_mapping[airline]
            }
        )
        return alliances

    def __airlines(self) -> "SerpFlightSearchParamBuilder":
        preferred_airlines = (
            self.flight_params["preferred_airline_codes"] or self.flight_params["default_airline_brands"] or []
        )
        if ("HA" in preferred_airlines) != ("AS" in preferred_airlines):
            preferred_airlines = list(set(preferred_airlines + ["AS", "HA"]))

        if self.flight_params["preferred_airline_codes"]:
            alliances = SerpFlightSearchParamBuilder.get_alliances_for_airlines(preferred_airlines)
            self.request_params[SerpRequestParam.AIRLINES_ALLIANCES] = ",".join(preferred_airlines + alliances)
        elif preferred_airlines:
            self.request_params[SerpRequestParam.AIRLINES_ALLIANCES] = ",".join(preferred_airlines)

        return self

    def __cabin(self) -> "SerpFlightSearchParamBuilder":
        preferred_cabin = self.flight_params["preferred_cabin"] or []
        self.request_params[SerpRequestParam.TRAVEL_CLASS] = TravelClass.from_value(
            preferred_cabin[0] if preferred_cabin else None
        ).value
        return self

    def __stops(self) -> "SerpFlightSearchParamBuilder":
        stops = self.flight_params["number_of_stops"] if self.flight_params["number_of_stops"] is not None else 0
        self.request_params[SerpRequestParam.STOPS] = str(NumOfStops.from_value(stops).value)
        return self

    def __times(self) -> "SerpFlightSearchParamBuilder":
        def __valid_time_range(time_range_str: str):
            try:
                time_range = time_range_str.split("-")
                if len(time_range) != 2:
                    return "0,23"
                start, end = time_range
                if all(map(lambda x: x.isdigit() and 0 <= int(x) <= 24, time_range)) and int(start) < int(end):
                    # Check if the time window is too narrow, and expand it to the minimum required size
                    if int(end) - int(start) < MIN_FLIGHT_SEARCH_WINDOW:
                        logger.warning(f"Narrow time window detected ({time_range_str}), expanding search range.")
                        new_start = max(0, int(start) - 1)
                        new_end = min(23, new_start + MIN_FLIGHT_SEARCH_WINDOW)
                        return f"{new_start},{new_end}"
                    return f"{start},{end}"
                return "0,23"
            except ValueError:
                return "0,23"

        # Here we only pick up the outbound time range from the flight_params
        # because otherwise the flight search cache would be less likely to hit
        outbound_time_range = ""
        preferred_outbound_departure_times = self.flight_params["outbound_departure_time"]
        if preferred_outbound_departure_times:
            outbound_time_range = __valid_time_range(preferred_outbound_departure_times)

        preferred_outbound_arrival_times = self.flight_params["outbound_arrival_time"]
        if preferred_outbound_arrival_times:
            outbound_time_range = (
                "0,23" if not outbound_time_range else __valid_time_range(outbound_time_range)
            ) + f",{__valid_time_range(preferred_outbound_arrival_times)}"

        if outbound_time_range:
            self.request_params[SerpRequestParam.OUTBOUND_TIMES] = outbound_time_range

        return self

    def __custom(self, **kwargs) -> "SerpFlightSearchParamBuilder":
        """
        Allows adding custom parameters to the current request_params.
        """
        self.request_params.update(**kwargs)
        return self

    def build(self) -> Dict[str, Any]:
        return self.request_params

    @classmethod
    def build_flight_search_params(
        cls,
        flight_params: FlightSearchParams,
        include=("airlines", "cabin", "stops", "times"),
        custom_params: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        builder = cls(flight_params)
        for method_name in include:
            getattr(builder, f"_{cls.__name__}__{method_name}")()
        if custom_params:
            builder.__custom(**custom_params)
        return builder.build()


class SerpFlightSearchHelper:
    def __init__(self, user: User, thread: ChatThread, timezone: str | None):
        self.user = user
        self.thread = thread
        self.timezone = timezone
        self.flight_search_params: Dict[str, Any] = {}
        self._lock = asyncio.Lock()  # Async lock for write operations

    # ==========================================
    # Flight Search and API Calls
    # ==========================================
    async def __search_flights(
        self,
        params: Dict[str, Any],
        request_filename: str | None = "serp_flightsearch_request.json",
        response_filename: str | None = "serp_flightsearch_response.json",
        type: str = "",
    ) -> Dict[str, Any]:
        """
        Search for flights using Serp API
        """
        try:
            if request_filename:
                flight_utils.write_to_file_async(request_filename, json.dumps(params, indent=4), "a")

            response = await google_serp_api.search_flights(params)

            if response_filename:
                flight_utils.write_to_file_async(response_filename, json.dumps(response, indent=4), "a")

            flight_utils.log_request_to_firehose(
                source="SERP",
                trip_id=self.thread.id,
                user_id=self.user.id,
                method=type,
                params=params,
                response=response,
            )
            return response
        except Exception as e:
            logger.error(f"Error searching flights with SerpAPI: {str(e)}")
            raise

    async def __search_departure_flights(self, params: dict) -> Dict[str, Any]:
        """Search for departure flights."""
        metrics_service.count(metric_tags=[], metric_name="serp.departure_flight_request")
        return await self.__search_flights(
            params, "serp_departure_request.json", "serp_departure_response.json", "DEPARTURE_SEARCH"
        )

    async def __search_next_flights(self, params: dict, departure_token: str, **kwargs) -> Dict[str, Any]:
        """Search for return flights. One way doesn't need to do this search."""
        flight_params = {**params, SerpRequestParam.DEPARTURE_TOKEN: departure_token, **kwargs}
        metrics_service.count(metric_tags=[], metric_name="serp.next_flight_request")
        return await self.__search_flights(
            flight_params, "serp_next_flight_request.json", "serp_next_flight_response.json", "RETURN_SEARCH"
        )

    async def __search_booking_flights(self, params: dict, booking_token: Optional[str]) -> Dict[str, Any]:
        """Search for booking flights with built params."""
        token_to_use = booking_token if booking_token is not None else ""
        flight_params = {**params, SerpRequestParam.BOOKING_TOKEN: token_to_use}
        metrics_service.count(metric_tags=[], metric_name="serp.booking_flight_request")
        return await self.__search_flights(
            flight_params, "serp_booking_request.json", "serp_booking_response.json", "BOOKING_SEARCH"
        )

    async def __search_return_and_booking_flights(
        self, departure_token: str, departure_airline: str
    ) -> tuple[Dict[str, Any], Optional[Booking]]:
        flight_choices = {}

        # Get alliances for the airline and include them in the search
        alliances = SerpFlightSearchParamBuilder.get_alliances_for_airlines([departure_airline])
        include_airlines = ",".join(alliances + [departure_airline]) if alliances else departure_airline

        # Search for return flights with departure flight airline and its alliances
        return_flights = await self.__search_next_flights(
            self.flight_search_params, departure_token, include_airlines=include_airlines
        )
        return_flight_id = return_flights["search_metadata"]["id"]

        parsed_return_flights = SerpFlightParser.parse_flight_search_response(return_flights)
        if not parsed_return_flights.flight_details:
            logger.error(
                f"Error searching return flight {return_flight_id}: {return_flights.get('error', 'Unknown error')}"
            )
            return {}, None

        booking_tokens = [flight_detail.booking_token for flight_detail in parsed_return_flights.flight_details]
        flight_choices[return_flight_id] = {}
        flight_choices[return_flight_id]["return_flights"] = parsed_return_flights.flight_details
        flight_choices[return_flight_id]["booking_tokens"] = booking_tokens

        # Returned result is ordered by price.
        booking_token = ""
        if parsed_return_flights.flight_details:
            cheaptest_flight = min(
                parsed_return_flights.flight_details, key=lambda flight: flight.price if flight.price else float("inf")
            )
            booking_token = cheaptest_flight.booking_token or ""

        booking_options_result = await self.__search_booking_flights(self.flight_search_params, booking_token)
        parsed_booking_options = SerpFlightParser.parse_booking_option_response(booking_options_result)
        parsed_booking_options.flight_search_id = return_flight_id
        booking: Optional[Booking] = parsed_booking_options if parsed_booking_options.booking_options else None

        return flight_choices, booking

    async def __handle_one_way_flight(
        self,
        parsed_flights: Flight,
        flight_choices: dict[str, Any],
    ) -> Tuple[dict[str, Any], List[Booking]]:
        """
        Handles booking options for one-way flights.
        """
        t = Timings("SERP: search_one_way_flights")
        request_id = parsed_flights.flight_search_id
        booking_tokens = [
            flight_detail.booking_token
            for flight_detail in parsed_flights.flight_details
            if flight_detail.booking_token is not None
        ]

        if not booking_tokens:
            logger.warning(f"No valid booking tokens found for oneway flight search.\r\n{inspect(request_id)}")

        tasks = [self.__search_booking_flights(self.flight_search_params, token) for token in booking_tokens]

        logger.info(f"[SERP] {len(booking_tokens)} booking requests were sent.", mask=serp_flight_log_mask)
        booking_options_results = await asyncio.gather(*tasks) if tasks else []

        booking_options: list[Booking] = []
        for booking_option_result in booking_options_results:
            parsed_booking_option = SerpFlightParser.parse_booking_option_response(booking_option_result)
            if not parsed_booking_option.booking_options:
                continue
            parsed_booking_option.flight_search_id = request_id
            booking_options.append(parsed_booking_option)

        t.print_timing("green")
        return flight_choices, booking_options

    async def __handle_round_trip_flight(
        self,
        parsed_flights: Flight,
        flight_choices: dict[str, Any],
        schedule_send_message_fn: Callable[[dict[str, Any]], None],
    ) -> Tuple[dict[str, Any], List[Booking]]:
        """
        Handles return flight search for round-trip flights.
        """
        # Extract departure tokens
        t = Timings("SERP: search_round_trip_flights")
        departure_flights = []
        for flight_detail in parsed_flights.flight_details:
            if flight_detail.departure_token is not None:
                flight_parts = flight_detail.flights[0].flight_number.split(" ", 1)
                airline = flight_parts[0] if len(flight_parts) > 1 else ""
                departure_flights.append((flight_detail.departure_token, airline))

        if not departure_flights:
            logger.warning("No valid departure tokens found for {trip_type.name} flight")
        else:
            schedule_send_message_fn(
                {
                    "type": "flights_skeleton_async",
                    "text": f"Just pulled {len(departure_flights)} departure options — now grabbing return flights and pricing before I recommend the best ones for you. Sit tight…",
                    "isBotMessage": True,
                    "expectResponse": False,
                }
            )

        # Fetch return + booking options
        tasks = [self.__search_return_and_booking_flights(token, airline) for token, airline in departure_flights]

        logger.info(
            f"[SERP] total {len(departure_flights) * 2} return and booking requests were sent.",
            mask=serp_flight_log_mask,
        )
        flight_choices_results = await asyncio.gather(*tasks)
        booking_options: List[Booking] = []

        for flight_choices_result in flight_choices_results:
            flight_choices.update(flight_choices_result[0])
            if flight_choices_result[1]:
                booking_options.append(flight_choices_result[1])
        t.print_timing("green")

        await self.__cache_tokens(flight_choices)

        return flight_choices, booking_options

    async def __handle_multi_city_flight(
        self, parsed_flights: Flight, flight_choices: dict[str, Any]
    ) -> Tuple[dict[str, Any], List[Booking]]:
        t = Timings("SERP: search_multi_city")
        departure_tokens = [
            flight_detail.departure_token
            for flight_detail in parsed_flights.flight_details
            if flight_detail.departure_token is not None
        ]
        flight_choices = {}

        i = 1
        return_flight_id = None
        parsed_return_flights: Flight | None = None
        all_legs = json.loads(self.flight_search_params["multi_city_json"])
        final_booking_token = []
        first_return_flight_id = None
        while i < len(all_legs):
            i += 1
            new_departure_tokens = []

            for departure_token in departure_tokens:
                return_flights = await self.__search_next_flights(self.flight_search_params, departure_token)
                return_flight_id = return_flights["search_metadata"]["id"]
                if not first_return_flight_id:
                    first_return_flight_id = return_flight_id
                parsed_return_flights = SerpFlightParser.parse_flight_search_response(return_flights)
                if not parsed_return_flights or not parsed_return_flights.flight_details:
                    logger.error(
                        f"Error searching flight {return_flight_id}: {return_flights.get('error', 'Unknown error')}"
                    )
                flight_choices[return_flight_id] = {}
                flight_choices[return_flight_id]["return_flights"] = [
                    d.departure_token
                    for d in parsed_return_flights.flight_details or []
                    if d.departure_token is not None
                ]

                if parsed_return_flights.flight_details and parsed_return_flights.flight_details[0].departure_token:
                    new_departure_tokens.append(parsed_return_flights.flight_details[0].departure_token)
                else:
                    if parsed_return_flights.flight_details and parsed_return_flights.flight_details[0].booking_token:
                        final_booking_token.append(parsed_return_flights.flight_details[0].booking_token)
            if len(new_departure_tokens) == 0:
                break
            departure_tokens = new_departure_tokens

        if not parsed_return_flights:
            logger.error(f"No valid return flights found for {departure_tokens}")
            raise Exception(f"No valid return flights found for {departure_tokens}")

        if not departure_tokens:
            logger.warning("No valid departure tokens found for {trip_type.name} flight")

        if return_flight_id:
            flight_choices[return_flight_id] = {}
            flight_choices[return_flight_id]["return_flights"] = [
                d.departure_token for d in parsed_return_flights.flight_details or [] if d.departure_token is not None
            ]
            flight_choices[return_flight_id]["booking_tokens"] = final_booking_token

        async def __handle_booking_token(booking_token: str):
            booking_options_result = await self.__search_booking_flights(self.flight_search_params, booking_token)
            parsed_booking_options = SerpFlightParser.parse_booking_option_response(booking_options_result)
            parsed_booking_options.flight_search_id = first_return_flight_id
            booking: Optional[Booking] = parsed_booking_options if parsed_booking_options.booking_options else None
            return booking

        tasks = [__handle_booking_token(token) for token in final_booking_token]

        booking_option_results = await asyncio.gather(*tasks)
        booking_list = []
        for booking_option_result in booking_option_results:
            if booking_option_result:
                booking_list.append(booking_option_result)
        t.print_timing("green")
        await self.__cache_tokens(flight_choices)

        return flight_choices, booking_list

    @staticmethod
    def less_than_threshold(results: Flight) -> bool:
        return len(results.flight_details or []) <= FALLBACK_THRESHOLD

    @staticmethod
    def is_empty(results: Flight) -> bool:
        return not results.flight_details

    # Create partial functions with predefined arguments
    __full_search_params = SerpFlightSearchParamBuilder.build_flight_search_params
    __one_stop_search_params = partial(
        SerpFlightSearchParamBuilder.build_flight_search_params,
        custom_params={
            SerpRequestParam.STOPS: NumOfStops.ONE_STOP.value,  # directly override the stops parameter
            SerpRequestParam.SHOW_HIDDEN: "true",
        },
    )
    __base_only_serach_params = partial(
        SerpFlightSearchParamBuilder.build_flight_search_params,
        custom_params={SerpRequestParam.SHOW_HIDDEN: "true", SerpRequestParam.NO_CACHE: "true"},
    )

    def __attempt_action_generator(self, params: FlightSearchParams, is_premium_cabin_search: bool):
        last_attempt_params = tuple(["cabin"]) if is_premium_cabin_search else ()

        # If the user has a predefined preference for the number of stops or explicitly specifies it in the current search, we should not modify stops parameter.
        if not params["number_of_stops"]:
            yield 1, (self.less_than_threshold, self.__full_search_params(params))
            yield 2, (self.less_than_threshold, self.__one_stop_search_params(params))
            yield 3, (self.is_empty, self.__base_only_serach_params(params, include=last_attempt_params))
        else:
            yield 1, (self.less_than_threshold, self.__full_search_params(params))
            yield 2, (self.is_empty, self.__base_only_serach_params(params, include=last_attempt_params))

    async def __search_departure_flights_with_retry(
        self,
        params: FlightSearchParams,
        schedule_send_message_fn: Callable[[dict[str, Any]], None],
        is_premium_cabin_search: bool,
    ):
        """
        Attempts to search for flights with retries if the results are below a threshold.
        Logs each attempt's outcome, and returns the search request ID and parsed flight data.
        """
        parsed_flights = None

        for attempt, (should_retry, flight_params) in self.__attempt_action_generator(params, is_premium_cabin_search):
            if attempt > 1:
                schedule_send_message_fn(
                    {
                        "type": "flights_skeleton_async",
                        "text": f"I didn't find any flights that exactly match your preferences, so I'm searching {'a bit' if attempt == 2 else 'even'} deeper to find the best options for you ...",
                        "isBotMessage": True,
                        "expectResponse": False,
                    }
                )

            await self.__update_flight_search_params(flight_params)
            flight_results = await self.__search_departure_flights(flight_params)
            parsed_flights = SerpFlightParser.parse_flight_search_response(flight_results)
            request_id = parsed_flights.flight_search_id

            if should_retry(parsed_flights):
                logger.warning(
                    f"Flight search attempt {attempt} got {len(parsed_flights.flight_details)} results, less than threshold {FALLBACK_THRESHOLD}. Check more details: {inspect(request_id)}. ",
                    mask=serp_flight_log_mask,
                )
            else:
                logger.info(
                    f"Flight search attempt {attempt} succeeded with {len(parsed_flights.flight_details)} flights found. Request details: {inspect(request_id)}. ",
                    mask=serp_flight_log_mask,
                )
                return parsed_flights

        return parsed_flights

    async def search_flights_serp(
        self,
        params: FlightSearchParams,
        schedule_send_message_fn: Callable[[dict[str, Any]], None],
        enable_retry: bool = True,
    ) -> Tuple[dict[str, Any], List[Booking]]:
        # trip_type = TripType.ROUNDTRIP if params.get("return_date", None) else TripType.ONEWAY
        trip_type = (
            TripType.ONEWAY
            if params["flight_type"] == FlightType.OneWay
            else TripType.ROUNDTRIP
            if params["flight_type"] == FlightType.RoundTrip
            else TripType.MULTI_LEGS
        )
        is_premium_cabin_search = flight_utils.is_premium_cabin(params.get("preferred_cabin"))
        logger.info(
            f"Searching {'premium' if is_premium_cabin_search else ''} flights on SERP for {trip_type.name} trip",
            mask=serp_flight_log_mask,
        )

        flight_choices: dict[str, Any] = {}
        t = Timings("SERP: search_departure_flights")

        parsed_flights = None
        if enable_retry:
            parsed_flights = await self.__search_departure_flights_with_retry(
                params, schedule_send_message_fn, is_premium_cabin_search
            )
        else:
            flight_search_params = SerpFlightSearchParamBuilder.build_flight_search_params(
                params,
                include=tuple(["stops"]),
                custom_params={SerpRequestParam.SHOW_HIDDEN: "true", SerpRequestParam.NO_CACHE: "false"},
            )
            departure_flight = await self.__search_departure_flights(flight_search_params)
            await self.__update_flight_search_params(flight_search_params)
            parsed_flights = SerpFlightParser.parse_flight_search_response(departure_flight)

        t.print_timing("green")

        if not parsed_flights:
            return flight_choices, []

        request_id = parsed_flights.flight_search_id
        flight_choices[request_id] = {}
        flight_choices[request_id]["departure_flights"] = parsed_flights.flight_details

        if trip_type == TripType.ONEWAY:
            return await self.__handle_one_way_flight(parsed_flights, flight_choices)
        elif trip_type == TripType.ROUNDTRIP:
            return await self.__handle_round_trip_flight(parsed_flights, flight_choices, schedule_send_message_fn)
        else:
            raise ValueError("Invalid trip type. Use 'one_way' or 'round_trip' or 'multi_legs'.")

    async def search_return_flights_serp(
        self, flight_id: str, flight_choices: dict[str, Any], is_last_segment: bool | None
    ):
        t = Timings("SERP: search_return_flights_serp")
        cache = RemoteCache()
        cached_data = await cache.get(
            SerpFlightSearchHelper.__create_cached_key(
                flight_id,
                BOOKING_TOKEN_TABLE_PREFIX if is_last_segment is None or is_last_segment else DEPARTURE_TABLE_PREFIX,
            )
        )
        tokens = json.loads(cached_data) if cached_data else []
        if not tokens:
            tokens = flight_choices[flight_id]["booking_tokens"]  # fallback to saved dict

        await self.__load_flight_search_params()
        if is_last_segment is None or is_last_segment is True:
            tasks = [self.__search_booking_flights(self.flight_search_params, token) for token in tokens]
        else:
            tasks = [self.__search_next_flights(self.flight_search_params, token) for token in tokens]

        flight_choices_results = await asyncio.gather(*tasks)

        booking_options = []
        for booking_option_result in flight_choices_results:
            if is_last_segment is None or is_last_segment is True:
                parsed_booking_option = SerpFlightParser.parse_booking_option_response(booking_option_result)
            else:
                parsed_booking_option = SerpFlightParser.parse_flight_search_response(booking_option_result)
            if isinstance(parsed_booking_option, Booking) and not parsed_booking_option.booking_options:
                continue
            if isinstance(parsed_booking_option, Flight) and not parsed_booking_option.flight_details:
                continue
            parsed_booking_option.flight_search_id = flight_id
            booking_options.append(parsed_booking_option)
        t.print_timing("green")
        return flight_choices, booking_options

    # ==========================================
    # Flight Recommandation
    # ==========================================
    @staticmethod
    def __process_flight_stop(stops: List[Stop], option_title: Optional[str] = None) -> List[FlightStop]:
        flight_stops = []
        for stop in stops:
            flight_parts = stop.flight_number.split(" ", 1)
            airline_code = flight_parts[0] if len(flight_parts) > 1 else ""
            flight_number = flight_parts[1] if len(flight_parts) > 1 else ""

            flight_stops.append(
                FlightStop(
                    departure_time=stop.departure_airport[Airport.TIME],
                    arrival_time=stop.arrival_airport[Airport.TIME],
                    departure_timezone=get_timezone_from_iata_airport_code(
                        stop.departure_airport[Airport.ID],
                    ),
                    arrival_timezone=get_timezone_from_iata_airport_code(
                        stop.arrival_airport[Airport.ID],
                    ),
                    airline_code=airline_code,
                    airline_name=stop.airline,
                    flight_number=flight_number,
                    departure_airport_code=stop.departure_airport[Airport.ID],
                    departure_airport_name=stop.departure_airport[Airport.NAME],
                    arrival_airport_code=stop.arrival_airport[Airport.ID],
                    arrival_airport_name=stop.arrival_airport[Airport.NAME],
                    cabin=option_title,  # serp doesn't have cabin class info, we use option_title instead for now
                    airplane=stop.airplane,
                    legroom=stop.legroom,
                )
            )

        return flight_stops

    @staticmethod
    def is_exchangeable_fare_option(airline_code, fare_option):
        """Checks if a flight ticket is exchangeable based on airline code and fare option."""
        if fare_option is None:
            return True
        fare_option = fare_option.strip().lower()
        if airline_code in NON_EXCHANGABLE_FARES_MAP and fare_option in NON_EXCHANGABLE_FARES_MAP[airline_code]:
            return False  # Not exchangeable
        return True  # Exchangeable

    @staticmethod
    def process_flight_search_results(
        bookings: List[Booking], is_returned: bool = False, current_segment_index: int | None = None
    ):
        # Process depature flights
        depature_flight_choices: List[dict[str, Any]] = []
        serp_flight_options: List[SerpFlightOption] = []
        index = 0
        for booking in bookings:
            booking_search_id = booking.booking_search_id
            flight_search_id = booking.flight_search_id

            if current_segment_index is not None:
                selected_flight = booking.selected_flights[current_segment_index]
            else:
                selected_flight = booking.selected_flights[0] if not is_returned else booking.selected_flights[1]
            stops = selected_flight.flights

            departure_code_id = stops[0].departure_airport[Airport.ID]
            departure_time = stops[0].departure_airport[Airport.TIME]
            arrival_code_id = stops[-1].arrival_airport[Airport.ID]
            arrival_time = stops[-1].arrival_airport[Airport.TIME]
            flight_parts = stops[0].flight_number.split(" ", 1)
            airline_code = flight_parts[0] if len(flight_parts) > 1 else ""

            for id, booking_option in enumerate(booking.booking_options, start=0):
                # Filter out basic economy flights which are not exchangable
                is_exchangable = SerpFlightSearchHelper.is_exchangeable_fare_option(
                    airline_code, booking_option.option_title
                )

                if not is_exchangable:
                    continue

                extensions = booking_option.extensions if booking_option.extensions else {}

                def find_policy(keywords):
                    return next((ext for ext in extensions if any(kw in ext.lower() for kw in keywords)), None)

                flight_data = {
                    "index_id": index,
                    "origin": departure_code_id,
                    "destination": arrival_code_id,
                    "departure_time": departure_time,
                    "arrival_time": arrival_time,
                    "airline_code": airline_code,
                    "flight_number": flight_parts[1] if len(flight_parts) > 1 else "",
                    "number_of_stops": len(stops) - 1,
                    "duration": minutes_to_iso_duration(selected_flight.total_duration),
                    "aircraft_name": stops[0].airplane,
                    "price": booking_option.price,
                    "fare_option_name": booking_option.option_title,
                    "legroom": stops[0].legroom,
                    "cancellation_policy": find_policy(["refund", "cancel"])
                    or (
                        "refundable"
                        if booking_option.option_title and "refundable" in booking_option.option_title
                        else None
                    )
                    or None,
                    "exchange_policy": find_policy(["change", "exchange"]),
                    "seat_selection_policy": find_policy(["seat"]),
                    "boarding_policy": find_policy(["board"]),
                    "baggage_policy": ",".join(booking_option.baggage_prices),
                }
                flight_data["departure_time_category"] = (
                    f"{determine_time_category(flight_data['departure_time'])} departure"
                )
                flight_data["arrival_time_category"] = f"{determine_time_category(flight_data['arrival_time'])} arrival"

                assert flight_search_id is not None, (
                    "when ready to present serp flight option, it must have flight_search_id"
                )

                flight_option = SerpFlightOption(
                    serp_flight_id=generate_serp_flight_option_id(
                        is_returned=is_returned,
                        booking_search_id=booking_search_id,
                        flight_search_id=flight_search_id,
                        id=id,
                    ),
                    stops=SerpFlightSearchHelper.__process_flight_stop(stops, booking_option.option_title),
                    departure_airport_code=departure_code_id,
                    arrival_airport_code=arrival_code_id,
                    departure_time=departure_time,
                    arrival_time=arrival_time,
                    total_price=booking_option.price,
                    total_duration=selected_flight.total_duration,
                    cancellation_policy=flight_data["cancellation_policy"],
                    exchange_policy=flight_data["exchange_policy"],
                    seat_selection_policy=flight_data["seat_selection_policy"],
                    fare_option_name=booking_option.option_title,
                    airline_code=airline_code,
                    flight_number=flight_parts[1] if len(flight_parts) > 1 else "",
                    iso_duration=minutes_to_iso_duration(selected_flight.total_duration),
                    baggage_policy=flight_data["baggage_policy"],
                    boarding_policy=flight_data["boarding_policy"],
                )
                flight_data["dedup_key"] = flight_option.get_flight_option_dedup_key()
                index += 1
                depature_flight_choices.append(flight_data)
                serp_flight_options.append(flight_option)  # array index map to id_token_key

        new_flight_response = csv_from_dict(depature_flight_choices, "serp_flightsearch_choices_for_llm.csv")

        return new_flight_response, serp_flight_options, depature_flight_choices

    # ==========================================
    # Internal caching
    # ==========================================
    async def __load_flight_search_params(self):
        cache = RemoteCache()
        key = self.__create_cached_key(self.thread.id, SERP_SEARCH_PARAM_TABLE_PREFIX)
        value = await cache.get(key)
        self.flight_search_params = json.loads(value) if value else {}
        return self.flight_search_params

    async def __update_flight_search_params(self, params: Dict[str, Any]) -> bool:
        async with self._lock:
            self.flight_search_params = params
        cache = RemoteCache()
        res = await cache.set(
            self.__create_cached_key(self.thread.id, SERP_SEARCH_PARAM_TABLE_PREFIX),
            json.dumps(params),
            expire=timedelta(hours=24),
        )
        return res

    @staticmethod
    def __create_cached_key(key_id: Any, prefix: str) -> str:
        return f"{prefix}:{key_id}"

    @staticmethod
    async def __cache_tokens(flight_choices: Dict[str, Any]):
        cache = RemoteCache()
        flight_choices_to_cache = {}
        for key, value in flight_choices.items():
            if "booking_tokens" in value:
                cache_key = SerpFlightSearchHelper.__create_cached_key(key, BOOKING_TOKEN_TABLE_PREFIX)
                cache_value = json.dumps(value.get("booking_tokens", []))
                flight_choices_to_cache[cache_key] = cache_value

        await cache.batch_set(flight_choices_to_cache, expire=timedelta(hours=settings.SERP_FLIGHT_TOKEN_CACHE_TTL))
