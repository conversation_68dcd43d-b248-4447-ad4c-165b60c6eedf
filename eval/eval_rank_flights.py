from langfuse import Langfuse, observe

from baml_client import b
from eval.constants import LF
from server.utils.settings import settings

# import env variables
print(settings.OTTO_ENV)

# Initialize Langfuse client
langfuse = Langfuse()


def eval(expected, result) -> float:
    """Evaluate the flight picking results by comparing expected and actual rankings."""

    score = 0
    if not expected or not result:
        return score

    expected_rank = [
        int(expected.get("Rank 1")),
        int(expected.get("Rank 2")),
        int(expected.get("Rank 3")),
        int(expected.get("Rank 4")),
        int(expected.get("Rank 5")),
        int(expected.get("Rank 6")),
    ]
    result_rank = [
        result.fc_1.index_id,
        result.fc_2.index_id,
        result.fc_3.index_id,
        result.fc_4.index_id,
        result.fc_5.index_id,
        result.fc_6.index_id,
    ]

    length = min(len(expected_rank), len(result_rank))
    for i in range(length):
        if result_rank[i] in expected_rank:
            score += 0.5
        if expected_rank[i] == result_rank[i]:
            score += 1
    return score / length if length > 0 else 0


@observe()
async def run_llm(input_data):
    r = await b.ProcessFlightSearchResults_v2(
        messages=[f"user: {input_data.get('query_prompt')}"],
        travel_context=input_data.get("user_preferences"),
        results=input_data.get("flights_csv"),
        current_date=input_data.get("today"),
        alliance_airlines="",
        self_intro=None,
        convo_style=None,
        preferred_flight_cabin=None,
        user_preferred_airline_codes=[],
        airport_default_airline_codes=[],
        airport_code="",
        is_premium_search=False,
    )
    return r


async def run_experiemnt():
    experiment_name = LF.Experiment.FLIGHT_PICKING_EXPERIMENT.value
    dataset = langfuse.get_dataset(LF.DataSet.FLIGHT_RANKING_BENCHMARK.value)

    for item in dataset.items:
        with item.run(run_name=experiment_name) as span:
            result = await run_llm(item.input)
            score = eval(item.expected_output, result)
            span.score_trace(name=LF.Score.FLIGHT_PICKING_SCORE.value, value=score)


if __name__ == "__main__":
    if langfuse.auth_check():
        print("Langfuse client is authenticated and ready!")
    else:
        print("Authentication failed. Please check your credentials and host.")

    # import asyncio
    # asyncio.run(run_experiemnt())
